@import url("https://fonts.googleapis.com/css2?family=Genos:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Inter:wght@200;300;400;500;600;700&display=swap");
/* ========================= Css Variables Start ======================== */
:root {
  /* Font Family */
  --heading-font: "Genos", sans-serif;
  --body-font: "Inter", sans-serif;
  /* ========================= Color Variables Start =========================== */
  --white: 0 0% 100%;
  --light-h: 0;
  --light-s: 1%;
  --light-l: 53%;
  --light: var(--light-h) var(--light-s) var(--light-l);
  --black-h: 211;
  --black-s: 32%;
  --black-l: 14%;
  --black: var(--black-h) var(--black-s) var(--black-l);
  --heading-color: var(--white);
  --body-color: var(--white) / 0.6;
  --border-color: 0 0% 88%;
  --section-bg: 208 100% 97%;
  /* ================================ Box Shadow Start =============================== */
  --header-box-shadow: 0px -1px 15px 3px hsl(var(--black) / 0.3);
  --mobile-box-shadow: 0px -1px 5px 0px hsl(var(--black) / 0.92);
  --box-shadow: 0px 2px 15px hsl(var(--black) / 0.05);
  /* ================================ Box Shadow End =============================== */
  /* ========================= Base Color ============================= */
  --base-h: 35;
  --base-s: 53%;
  --base-l: 44%;
  --base: var(--base-h) var(--base-s) var(--base-l);

  /* Base Darken  */
  --base-d-100: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.1);
  --base-d-200: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.2);
  --base-d-300: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.3);
  --base-d-400: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.4);
  --base-d-500: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.5);
  --base-d-600: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.6);
  --base-d-700: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.7);
  --base-d-700: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.7);
  --base-d-800: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.8);
  --base-d-900: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.9);
  /* Base Lighten */
  --base-l-100: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.1);
  --base-l-200: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.2);
  --base-l-300: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.3);
  --base-l-400: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.4);
  --base-l-500: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.5);
  --base-l-600: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.6);
  --base-l-700: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.7);
  --base-l-800: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.8);
  --base-l-900: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.9);
  --base-gradient: linear-gradient(
    180deg,
    hsl(var(--base-two)),
    hsl(var(--base))
  );
  /* ========================= Base Two Color ============================= */
  --base-two-h: 51;
  --base-two-s: 66%;
  --base-two-l: 67%;
  --base-two: var(--base-two-h) var(--base-two-s) var(--base-two-l);
  /* Base Two Darken */
  --base-two-d-100: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.05);
  --base-two-d-200: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.1);
  --base-two-d-300: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.2);
  --base-two-d-400: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.3);
  --base-two-d-500: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.4);
  --base-two-d-600: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.5);
  --base-two-d-700: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.6);
  --base-two-d-800: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.7);
  --base-two-d-900: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.8);
  /* Base Two Lighten */
  --base-two-l-100: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.05);
  --base-two-l-200: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.1);
  --base-two-l-300: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.2);
  --base-two-l-400: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.3);
  --base-two-l-500: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.4);
  --base-two-l-600: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.5);
  --base-two-l-700: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.6);
  --base-two-l-800: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.7);
  --base-two-l-900: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.8);
  /* ============================== Bootstrap Modifier Start ============================== */
  /* Primary Color */
  --primary-h: 211;
  --primary-s: 100%;
  --primary-l: 50%;
  --primary: var(--primary-h) var(--primary-s) var(--primary-l);
  /* Primary Darken */
  --primary-d-100: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.1);
  --primary-d-200: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.2);
  --primary-d-300: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.3);
  --primary-d-400: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.4);
  --primary-d-500: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.5);
  /* primary Lighten */
  --primary-l-100: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.1);
  --primary-l-200: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.2);
  --primary-l-300: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.3);
  --primary-l-400: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.4);
  --primary-l-500: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.5);
  --primary-l-600: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.6);
  /* Secondary Color */
  --secondary-h: 208;
  --secondary-s: 7%;
  --secondary-l: 46%;
  --secondary: var(--secondary-h) var(--secondary-s) var(--secondary-l);
  /* Secondary Darken */
  --secondary-d-100: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.1);
  --secondary-d-200: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.2);
  --secondary-d-300: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.3);
  --secondary-d-400: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.4);
  --secondary-d-500: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.5);
  /* secondary Lighten */
  --secondary-l-100: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.1);
  --secondary-l-200: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.2);
  --secondary-l-300: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.3);
  --secondary-l-400: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.4);
  --secondary-l-500: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.5);
  --secondary-l-600: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.6);
  /* Success Color */
  --success-h: 148;
  --success-s: 98%;
  --success-l: 38%;
  --success: var(--success-h) var(--success-s) var(--success-l);
  /* Success Darken */
  --success-d-100: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.1);
  --success-d-200: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.2);
  --success-d-300: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.3);
  --success-d-400: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.4);
  --success-d-500: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.5);
  /* Success Lighten */
  --success-l-100: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.1);
  --success-l-200: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.2);
  --success-l-300: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.3);
  --success-l-400: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.4);
  --success-l-500: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.5);
  --success-l-600: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.6);
  /* Danger Color */
  --danger-h: 0;
  --danger-s: 96%;
  --danger-l: 63%;
  --danger: var(--danger-h) var(--danger-s) var(--danger-l);
  /* Danger Darken */
  --danger-d-100: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.1);
  --danger-d-200: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.2);
  --danger-d-300: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.3);
  --danger-d-400: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.4);
  --danger-d-500: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.5);
  /* danger Lighten */
  --danger-l-100: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.1);
  --danger-l-200: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.2);
  --danger-l-300: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.3);
  --danger-l-400: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.4);
  --danger-l-500: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.5);
  --danger-l-600: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.6);
  /* Warning Color */
  --warning-h: 29;
  --warning-s: 100%;
  --warning-l: 63%;
  --warning: var(--warning-h) var(--warning-s) var(--warning-l);
  /* Warning Darken */
  --warning-d-100: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.1);
  --warning-d-200: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.2);
  --warning-d-300: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.3);
  --warning-d-400: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.4);
  --warning-d-500: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.5);
  /* Warning Lighten */
  --warning-l-100: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.1);
  --warning-l-200: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.2);
  --warning-l-300: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.3);
  --warning-l-400: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.4);
  --warning-l-500: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.5);
  --warning-l-600: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.6);
  /* Info Color */
  --info-h: 196;
  --info-s: 100%;
  --info-l: 50%;
  --info: var(--info-h) var(--info-s) var(--info-l);
  /* Info Darken */
  --info-d-100: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.1);
  --info-d-200: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.2);
  --info-d-300: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.3);
  --info-d-400: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.4);
  --info-d-500: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.5);
  --info-l-100: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.1);
  --info-l-200: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.2);
  --info-l-300: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.3);
  --info-l-400: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.4);
  --info-l-500: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.5);
  --info-l-600: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.6);
  /* dark Color */
  --dark-h: 210;
  --dark-s: 40%;
  --dark-l: 10%;
  --dark: var(--dark-h) var(--dark-s) var(--dark-l);
  /* dark Darken */
  --dark-d-100: var(--dark-h) var(--dark-s)
    calc(var(--dark-l) - var(--dark-l) * 0.1);
  --dark-d-200: var(--dark-h) var(--dark-s)
    calc(var(--dark-l) - var(--dark-l) * 0.2);
  --dark-d-300: var(--dark-h) var(--dark-s)
    calc(var(--dark-l) - var(--dark-l) * 0.3);
  --dark-d-400: var(--dark-h) var(--dark-s)
    calc(var(--dark-l) - var(--dark-l) * 0.4);
  --dark-d-500: var(--dark-h) var(--dark-s)
    calc(var(--dark-l) - var(--dark-l) * 0.5);
  --dark-l-100: var(--dark-h) calc(var(--dark-s))
    calc(var(--dark-l) + (100% - var(--dark-l)) * 0.1);
  --dark-l-200: var(--dark-h) calc(var(--dark-s))
    calc(var(--dark-l) + (100% - var(--dark-l)) * 0.2);
  --dark-l-300: var(--dark-h) calc(var(--dark-s))
    calc(var(--dark-l) + (100% - var(--dark-l)) * 0.3);
  --dark-l-400: var(--dark-h) calc(var(--dark-s))
    calc(var(--dark-l) + (100% - var(--dark-l)) * 0.4);
  --dark-l-500: var(--dark-h) calc(var(--dark-s))
    calc(var(--dark-l) + (100% - var(--dark-l)) * 0.5);
  --dark-l-600: var(--dark-h) calc(var(--dark-s))
    calc(var(--dark-l) + (100% - var(--dark-l)) * 0.6);
  /* ============================== Bootstrap Modifier End ============================== */
}

/* ========================= Css Variables End =========================== */
/* ============================ Media Breakpoint for Each Device Start ============================ */
/* ================================== Font Size For responsive devices End =============================== */
/* ================================= Common Typography Css Start =========================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--body-font);
  color: hsl(var(--body-color));
  word-break: break-word;
  background-color: hsl(var(--dark));
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

p {
  font-weight: 400;
  margin: 0;
}

span {
  display: inline-block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--heading-font);
  color: hsl(var(--heading-color));
  line-height: 0.8;
  font-weight: 700;
  margin: 0;
}

h1 {
  font-size: clamp(2.25rem, 0.424rem + 3.803vw, 3.75rem);
}

h2 {
  font-size: clamp(1.5rem, -0.326rem + 3.803vw, 3rem);
}

h3 {
  font-size: clamp(1.375rem, 0.006rem + 2.853vw, 2.5rem);
}

h4 {
  font-size: clamp(1.25rem, 0.337rem + 1.902vw, 2rem);
}

h5 {
  font-size: clamp(1.125rem, 0.669rem + 0.951vw, 1.5rem);
}

h6 {
  font-size: clamp(1rem, 0.696rem + 0.634vw, 1.25rem);
}

h1 > a,
h2 > a,
h3 > a,
h4 > a,
h5 > a,
h6 > a {
  font-weight: inherit;
  font-size: inherit;
  color: inherit;
  transition: 0.2s linear;
  line-height: 1.3;
}

a {
  display: inline-block;
  transition: 0.2s linear;
  text-decoration: none;
  color: #0667f6;
}

a:hover {
  color: #2f83ff;
}

img {
  max-width: 100%;
  height: auto;
}

select {
  cursor: pointer;
}

ul,
ol {
  padding: 0;
  margin: 0;
  list-style: none;
}

button {
  border: 0;
  background-color: transparent;
}

button:focus {
  outline: none;
  box-shadow: none;
}

.form-select:focus {
  outline: 0;
  box-shadow: none;
}

/* ================================= Common Typography Css End =========================== */
/* ================================= Custom Classes Css Start =========================== */
/* Column Extra Small Screen */
@media screen and (min-width: 425px) and (max-width: 575px) {
  .col-xsm-6 {
    width: 50%;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1190px;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1377px;
  }
}
/* Section Background */
.section-bg {
  background-color: hsl(var(--section-bg));
}

/* Bg Image Css */
.bg-img {
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center center !important;
  width: 100%;
  height: 100%;
}

/* Hide Scroll bar Css For Custom Modal */
.scroll-hide {
  position: absolute;
  overflow-y: hidden;
  padding-right: 17px;
  top: 0;
  left: 0;
  width: 100%;
}

@media screen and (max-width: 991px) {
  .scroll-hide {
    padding-right: 0;
  }
}

.scroll-hide-sm {
  position: absolute;
  overflow-y: hidden;
  top: 0;
  left: 0;
  width: calc(100% - 0px);
}

/* Overlay Start */
.body-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  content: "";
  left: 0;
  top: 0;
  background-color: hsl(var(--black) / 0.6);
  z-index: 99;
  transition: 0.2s linear;
  visibility: hidden;
  opacity: 0;
}

.body-overlay.show-overlay {
  visibility: visible;
  opacity: 1;
}

.sidebar-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  content: "";
  left: 0;
  top: 0;
  background-color: hsl(var(--black) / 0.6);
  z-index: 99;
  transition: 0.2s linear;
  visibility: hidden;
  opacity: 0;
}

.sidebar-overlay.show {
  visibility: visible;
  opacity: 1;
  z-index: 999;
}

/* Overlay End */
/* ================================= Custom Classes Css End =========================== */
/* Fully Fit image Css */
.fit-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* ============================= Display Flex Css Start ============================= */
.flex-wrap,
.form--radio,
.form--check {
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 576px) {
  .flex-wrap,
  .form--radio,
  .form--check {
    font-size: 13px;
  }
}

.flex-align {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.flex-center {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

/* ============================= Display Flex Css End ============================= */
/* ============================= Positioning Css Class Start ===================== */
.pa-extend,
.alert__link::before {
  position: absolute;
  content: "";
}

.top-center-extend {
  top: 50%;
  transform: translateY(-50%);
}

.left-center-extend {
  left: 50%;
  transform: translateX(-50%);
}

.top-left-center-extend {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* ============================= Positioning Css Class End ===================== */
/* ===================== Font Size For responsive devices Start =================== */
.fs-10 {
  font-size: 0.625rem;
}

.fs-11 {
  font-size: 0.6875rem;
}

.fs-12,
.badge {
  font-size: 0.75rem;
}

.fs-13,
.form--check .form-check-input:checked::before {
  font-size: 0.8125rem;
}

.fs-14,
.form--radio .form-check-label {
  font-size: 0.875rem;
}

.fs-15,
.form--label,
.btn--icon {
  font-size: 0.9375rem;
}

.fs-16,
.info-card__list-link,
.info-card__desc,
.latest-blog__title a,
.alert__title,
.form--check .form-check-label,
.btn,
p {
  font-size: 1rem;
}

@media screen and (max-width: 1199px) {
  .fs-16,
  .info-card__list-link,
  .info-card__desc,
  .latest-blog__title a,
  .alert__title,
  .form--check .form-check-label,
  .btn,
  p {
    font-size: 0.875rem;
  }
}

.fs-17 {
  font-size: 1.0625rem;
}

@media screen and (max-width: 1199px) {
  .fs-17 {
    font-size: 1rem;
  }
}

@media screen and (max-width: 767px) {
  .fs-17 {
    font-size: 0.9375rem;
  }
}

.fs-18,
.socket-nav__link,
.socket-area__text,
.newsletter__input input::placeholder,
.newsletter__input input,
.form--control::placeholder,
.form--control {
  font-size: 1.125rem;
}

@media screen and (max-width: 1399px) {
  .fs-18,
  .socket-nav__link,
  .socket-area__text,
  .newsletter__input input::placeholder,
  .newsletter__input input,
  .form--control::placeholder,
  .form--control {
    font-size: 1.0625rem;
  }
}

@media screen and (max-width: 767px) {
  .fs-18,
  .socket-nav__link,
  .socket-area__text,
  .newsletter__input input::placeholder,
  .newsletter__input input,
  .form--control::placeholder,
  .form--control {
    font-size: 1rem;
  }
}

.fs-20,
.breadcrumb__item,
.social-list__link {
  font-size: 1.25rem;
}

@media screen and (max-width: 1399px) {
  .fs-20,
  .breadcrumb__item,
  .social-list__link {
    font-size: 1.125rem;
  }
}

@media screen and (max-width: 767px) {
  .fs-20,
  .breadcrumb__item,
  .social-list__link {
    font-size: 1.0625rem;
  }
}
/* ===================== Font Size For responsive devices End =================== */
/* ====================== Section Heading ==================== */
.section-heading {
  text-align: center;
  margin-bottom: 50px;
}

@media screen and (max-width: 1199px) {
  .section-heading {
    margin-bottom: 40px;
  }
}

@media screen and (max-width: 991px) {
  .section-heading {
    margin-bottom: 30px;
  }
}

.section-heading__title {
  position: relative;
  padding-bottom: 30px;
}

.section-heading__title .title-color {
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.section-heading__title .title-color::selection {
  -webkit-text-fill-color: hsl(var(--white));
}

@media screen and (max-width: 991px) {
  .section-heading__title {
    padding-bottom: 15px;
  }
}

.section-heading__desc {
  max-width: 530px;
  margin-left: auto;
  margin-right: auto;
  font-size: clamp(1rem, 0.696rem + 0.634vw, 1.25rem);
}

.section-heading.style-left {
  text-align: left;
}

.section-heading.style-left .section-heading__title::before {
  left: 0;
  transform: translateX(0);
}

.section-heading.style-left .section-heading__desc {
  margin-left: 0;
}

/* ====================== Section Heading En d==================== */
*::-webkit-scrollbar {
  width: 8px;
}

*::-webkit-scrollbar-track {
  background-color: hsl(var(--white) / 0.1);
  border-radius: 10px;
}

*::-webkit-scrollbar-thumb {
  background: var(--base-gradient);
  border-radius: 10px;
}

/* ================================= Background Color Css Start =========================== */
.bg--base {
  background-color: hsl(var(--base)) !important;
}

.bg--primary {
  background-color: hsl(var(--primary)) !important;
}

.bg--secondary {
  background-color: hsl(var(--secondary)) !important;
}

.bg--success {
  background-color: hsl(var(--success)) !important;
}

.bg--danger {
  background-color: hsl(var(--danger)) !important;
}

.bg--warning {
  background-color: hsl(var(--warning)) !important;
}

.bg--info {
  background-color: hsl(var(--info)) !important;
}

.bg--dark {
  background-color: hsl(var(--dark)) !important;
}

/* ================================= Background Color Css End =========================== */
/* ================================= Color Css Start =========================== */
.text--base {
  color: hsl(var(--base)) !important;
}

.text--primary {
  color: hsl(var(--primary)) !important;
}

.text--secondary {
  color: hsl(var(--secondary)) !important;
}

.text--success {
  color: hsl(var(--success)) !important;
}

.text--danger {
  color: hsl(var(--danger)) !important;
}

.text--warning {
  color: hsl(var(--warning)) !important;
}

.text--info {
  color: hsl(var(--info)) !important;
}

.text--dark {
  color: hsl(var(--dark)) !important;
}

.text--muted {
  color: hsl(var(--white) / 0.6);
}

/* ================================= Color Css End =========================== */
/* ================================= margin Css Start =========================== */
.my-100 {
  margin-top: 50px;
  margin-bottom: 50px;
}

@media (min-width: 576px) {
  .my-100 {
    margin-top: 80px;
    margin-bottom: 80px;
  }
}

@media (min-width: 992px) {
  .my-100 {
    margin-top: 100px;
    margin-bottom: 100px;
  }
}

.mt-100 {
  margin-top: 50px;
}

@media (min-width: 576px) {
  .mt-100 {
    margin-top: 80px;
  }
}

@media (min-width: 992px) {
  .mt-100 {
    margin-top: 100px;
  }
}

.mb-100 {
  margin-bottom: 50px;
}

@media (min-width: 576px) {
  .mb-100 {
    margin-bottom: 80px;
  }
}

@media (min-width: 992px) {
  .mb-100 {
    margin-bottom: 100px;
  }
}

.my-50 {
  margin-top: 30px;
  margin-bottom: 30px;
}

@media (min-width: 576px) {
  .my-50 {
    margin-top: 40px;
    margin-bottom: 40px;
  }
}

@media (min-width: 992px) {
  .my-50 {
    margin-top: 50px;
    margin-bottom: 50px;
  }
}

.mt-50 {
  margin-top: 30px;
}

@media (min-width: 576px) {
  .mt-50 {
    margin-top: 40px;
  }
}

@media (min-width: 992px) {
  .mt-50 {
    margin-top: 50px;
  }
}

.mb-50 {
  margin-bottom: 30px;
}

@media (min-width: 576px) {
  .mb-50 {
    margin-bottom: 40px;
  }
}

@media (min-width: 992px) {
  .mb-50 {
    margin-bottom: 50px;
  }
}
/* ================================= margin Css End =========================== */
/* ================================= padding Css Start =========================== */
.py-100 {
  padding-top: 50px;
  padding-bottom: 50px;
}

@media (min-width: 576px) {
  .py-100 {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}

@media (min-width: 992px) {
  .py-100 {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}

.pt-100 {
  padding-top: 50px;
}

@media (min-width: 576px) {
  .pt-100 {
    padding-top: 80px;
  }
}

@media (min-width: 992px) {
  .pt-100 {
    padding-top: 100px;
  }
}

.pb-100 {
  padding-bottom: 50px;
}

@media (min-width: 576px) {
  .pb-100 {
    padding-bottom: 80px;
  }
}

@media (min-width: 992px) {
  .pb-100 {
    padding-bottom: 100px;
  }
}

.py-50 {
  padding-top: 30px;
  padding-bottom: 30px;
}

@media (min-width: 576px) {
  .py-50 {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}

@media (min-width: 992px) {
  .py-50 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

.pt-50 {
  padding-top: 30px;
}

@media (min-width: 576px) {
  .pt-50 {
    padding-top: 40px;
  }
}

@media (min-width: 992px) {
  .pt-50 {
    padding-top: 50px;
  }
}

.pb-50 {
  padding-bottom: 30px;
}

@media (min-width: 576px) {
  .pb-50 {
    padding-bottom: 40px;
  }
}

@media (min-width: 992px) {
  .pb-50 {
    padding-bottom: 50px;
  }
}
/* ================================= padding Css End =========================== */
/* ================================= Border Color Css Start =========================== */
.border--base {
  border-color: hsl(var(--base)) !important;
}

.border--primary {
  border-color: hsl(var(--primary)) !important;
}

.border--secondary {
  border-color: hsl(var(--secondary)) !important;
}

.border--success {
  border-color: hsl(var(--success)) !important;
}

.border--danger {
  border-color: hsl(var(--danger)) !important;
}

.border--warning {
  border-color: hsl(var(--warning)) !important;
}

.border--info {
  border-color: hsl(var(--info)) !important;
}

.border--dark {
  border-color: hsl(var(--dark)) !important;
}

/* ================================= Border Color Css End =========================== */
/* =========================== Accordion Css start ============================= */
.custom--accordion {
  height: 392px;
  overflow-y: auto;
}

@media screen and (max-width: 991px) {
  .custom--accordion {
    height: auto;
  }
}

.custom--accordion::-webkit-scrollbar {
  width: 8px;
}

.custom--accordion::-webkit-scrollbar-track {
  background-color: hsl(var(--white) / 0.1);
  border-radius: 10px;
}

.custom--accordion::-webkit-scrollbar-thumb {
  background: var(--base-gradient);
  border-radius: 10px;
}

.custom--accordion .accordion-item {
  border: 1px solid hsl(var(--black) / 0.09);
  background-color: transparent !important;
  border-radius: 5px;
  overflow: hidden;
}

.custom--accordion .accordion-item:not(:last-child) {
  margin-bottom: 16px;
}

.custom--accordion .accordion-header {
  line-height: 1;
}

.custom--accordion .accordion-body {
  padding: 20px 4px 20px 0;
}

.custom--accordion .accordion-body .text {
  color: hsl(var(--light));
}

@media screen and (max-width: 575px) {
  .custom--accordion .accordion-body .text {
    max-width: 100%;
  }
}

.custom--accordion:first-of-type .accordion-button.collapsed {
  border-radius: 5px;
}

.custom--accordion:last-of-type .accordion-button.collapsed {
  border-radius: 5px;
}

.custom--accordion .accordion-button {
  background-color: hsl(var(--section-bg));
  background-color: hsl(var(--black)) !important;
  color: hsl(var(--white));
  padding: 15px;
  font-weight: 700;
  border: 1px solid hsl(var(--white) / 0.1);
}

.custom--accordion .accordion-button .accordion-title {
  font-size: 24px;
}

@media (max-width: 767px) {
  .custom--accordion .accordion-button .accordion-title {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .custom--accordion .accordion-button .accordion-title {
    font-size: 16px;
  }
}

@media screen and (max-width: 575px) {
  .custom--accordion .accordion-button {
    padding: 13px;
    padding-right: 30px;
  }
}

.custom--accordion .accordion-button::after {
  display: none;
}

.custom--accordion .accordion-button:focus {
  box-shadow: none;
}

.custom--accordion .accordion-button:not(.collapsed) {
  box-shadow: none;
}

.custom--accordion .accordion-button:not(.collapsed)::after {
  background-image: none;
}

.custom--accordion .accordion-button .accordion-icon {
  position: absolute;
  height: 100%;
  right: 0;
  width: 56px;
  border-left: 1px solid hsl(var(--white) / 0.1);
  display: grid;
  place-content: center;
}

.custom--accordion .accordion-button[aria-expanded="true"] .accordion-icon i,
.custom--accordion .accordion-button[aria-expanded="false"] .accordion-icon i {
  transition: all linear 0.3s;
  font-size: 20px;
  color: #979797;
  transform: rotate(180deg);
}

.custom--accordion .accordion-button[aria-expanded="false"] .accordion-icon i {
  top: auto;
  transform: rotate(0deg);
}

/* ================================= Accordion Css End =========================== */
/* ================================= Button Css Start =========================== */
.btn-check:checked + .btn,
.btn.active,
.btn.show,
.btn:first-child:active,
:not(.btn-check) + .btn:active {
  color: none;
  background-color: none;
  border-color: none;
}

.pill {
  border-radius: 40px !important;
}

.btn {
  color: hsl(var(--white)) !important;
  font-weight: 500;
  padding: 16px 29px;
  border-radius: 5px;
  position: relative;
  z-index: 1;
  border: 1px solid transparent;
  font-family: var(--body-font);
  line-height: 1;
}

@media screen and (max-width: 767px) {
  .btn {
    padding: 14px 25px;
    font-size: 0.875rem;
  }
}

.btn:hover,
.btn:focus,
.btn:focus-visible {
  box-shadow: none !important;
}

.btn:active {
  top: 1px;
}

.btn--gradient {
  background: linear-gradient(
    180deg,
    hsl(var(--base-l-300)) 5%,
    hsl(var(--base-two)) 108%
  );
  position: relative;
  z-index: 1;
  border-radius: 12px;
  border-bottom: 4px solid hsl(var(--base-l-100));
  font-size: 24px;
  font-family: var(--heading-font);
  color: hsl(var(--dark)) !important;
  padding: 8px 24px !important;
  font-weight: 700;
}

.btn--gradient::after {
  content: "";
  position: absolute;
  background: linear-gradient(
    0deg,
    hsl(var(--base-d-100)) -6%,
    hsl(var(--base-two-l-100)) 113%
  );
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  top: 50%;
  left: 50%;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  opacity: 1;
  z-index: -1;
  transition: all linear 0.3s;
}

.btn--gradient:hover::after {
  opacity: 0.2;
}

/* btn gradient success css start here  */
.btn--gradient-success {
  position: relative;
  z-index: 1;
  border-radius: 12px;
  border-bottom: 4px solid hsl(var(--base-l-100));
  font-size: 24px;
  font-family: var(--heading-font);
  color: hsl(var(--dark)) !important;
  padding: 8px 24px !important;
  font-weight: 700;
}

.btn--gradient-success::after {
  content: "";
  position: absolute;
  background: linear-gradient(
    0deg,
    hsl(var(--success-d-100)) -6%,
    hsl(var(--success-two-l-100)) 113%
  );
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  top: 50%;
  left: 50%;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  opacity: 1;
  z-index: -1;
  transition: all linear 0.3s;
}

/* btn gradient success end here  */

.btn--lg {
  padding: 20px 35px;
}

@media screen and (max-width: 991px) {
  .btn--lg {
    padding: 18px 30px;
  }
}

@media screen and (max-width: 767px) {
  .btn--lg {
    padding: 16px 25px;
  }
}

.btn--sm {
  padding: 10px 16px;
  font-size: 0.875rem;
}

@media screen and (max-width: 767px) {
  .btn--sm {
    padding: 8px 12px;
  }
}

.btn--icon {
  width: 35px;
  height: 35px;
  line-height: 35px;
  padding: 0;
}

.btn .icon {
  margin-right: 5px;
}

.btn--base {
  background-color: hsl(var(--base)) !important;
}

.btn--base:hover,
.btn--base:focus .btn--base:focus-visible {
  background-color: hsl(var(--base-d-200)) !important;
  border: 1px solid hsl(var(--base-d-200)) !important;
}

.btn-outline--base {
  background-color: transparent !important;
  border: 1px solid hsl(var(--base)) !important;
  color: hsl(var(--base)) !important;
}

.btn-outline--base:hover,
.btn-outline--base:focus .btn-outline--base:focus-visible {
  background-color: hsl(var(--base)) !important;
  color: hsl(var(--white)) !important;
}

.btn--primary {
  background-color: hsl(var(--primary)) !important;
}

.btn--primary:hover,
.btn--primary:focus .btn--primary:focus-visible {
  background-color: hsl(var(--primary-d-200)) !important;
  border: 1px solid hsl(var(--primary-d-200)) !important;
}

.btn-outline--primary {
  background-color: transparent !important;
  border: 1px solid hsl(var(--primary)) !important;
  color: hsl(var(--primary)) !important;
}

.btn-outline--primary:hover,
.btn-outline--primary:focus .btn-outline--primary:focus-visible {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--white)) !important;
}

.btn--secondary {
  background-color: hsl(var(--secondary)) !important;
}

.btn--secondary:hover,
.btn--secondary:focus .btn--secondary:focus-visible {
  background-color: hsl(var(--secondary-d-200)) !important;
  border: 1px solid hsl(var(--secondary-d-200)) !important;
}

.btn-outline--secondary {
  background-color: transparent !important;
  border: 1px solid hsl(var(--secondary)) !important;
  color: hsl(var(--secondary)) !important;
}

.btn-outline--secondary:hover,
.btn-outline--secondary:focus .btn-outline--secondary:focus-visible {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--white)) !important;
}

.btn--success {
  background-color: hsl(var(--success)) !important;
}

.btn--success:hover,
.btn--success:focus .btn--success:focus-visible {
  background-color: hsl(var(--success-d-200)) !important;
  border: 1px solid hsl(var(--success-d-200)) !important;
}

.btn-outline--success {
  background-color: transparent !important;
  border: 1px solid hsl(var(--success)) !important;
  color: hsl(var(--success)) !important;
}

.btn-outline--success:hover,
.btn-outline--success:focus .btn-outline--success:focus-visible {
  background-color: hsl(var(--success)) !important;
  color: hsl(var(--white)) !important;
}

.btn--danger {
  background-color: hsl(var(--danger)) !important;
}

.btn--danger:hover,
.btn--danger:focus .btn--danger:focus-visible {
  background-color: hsl(var(--danger-d-200)) !important;
  border: 1px solid hsl(var(--danger-d-200)) !important;
}

.btn-outline--danger {
  background-color: transparent !important;
  border: 1px solid hsl(var(--danger)) !important;
  color: hsl(var(--danger)) !important;
}

.btn-outline--danger:hover,
.btn-outline--danger:focus .btn-outline--danger:focus-visible {
  background-color: hsl(var(--danger)) !important;
  color: hsl(var(--white)) !important;
}

.btn--warning {
  background-color: hsl(var(--warning)) !important;
}

.btn--warning:hover,
.btn--warning:focus .btn--warning:focus-visible {
  background-color: hsl(var(--warning-d-200)) !important;
  border: 1px solid hsl(var(--warning-d-200)) !important;
}

.btn-outline--warning {
  background-color: transparent !important;
  border: 1px solid hsl(var(--warning)) !important;
  color: hsl(var(--warning)) !important;
}

.btn-outline--warning:hover,
.btn-outline--warning:focus .btn-outline--warning:focus-visible {
  background-color: hsl(var(--warning)) !important;
  color: hsl(var(--white)) !important;
}

.btn--info {
  background-color: hsl(var(--info)) !important;
}

.btn--info:hover,
.btn--info:focus .btn--info:focus-visible {
  background-color: hsl(var(--info-d-200)) !important;
  border: 1px solid hsl(var(--info-d-200)) !important;
}

.btn-outline--info {
  background-color: transparent !important;
  border: 1px solid hsl(var(--info)) !important;
  color: hsl(var(--info)) !important;
}

.btn-outline--info:hover,
.btn-outline--info:focus .btn-outline--info:focus-visible {
  background-color: hsl(var(--info)) !important;
  color: hsl(var(--white)) !important;
}

.btn--dark {
  background-color: hsl(var(--dark)) !important;
}

.btn--dark:hover,
.btn--dark:focus .btn--dark:focus-visible {
  background-color: hsl(var(--dark-d-200)) !important;
  border: 1px solid hsl(var(--dark-d-200)) !important;
}

.btn-outline--dark {
  background-color: transparent !important;
  border: 1px solid hsl(var(--dark)) !important;
  color: hsl(var(--dark)) !important;
}

.btn-outline--dark:hover,
.btn-outline--dark:focus .btn-outline--dark:focus-visible {
  background-color: hsl(var(--dark)) !important;
  color: hsl(var(--white)) !important;
}

/* ================================= Button Css End =========================== */
/* ================================= Card Css Start =========================== */
.custom--card {
  background-color: hsl(var(--black));
  border: 1px solid hsl(var(--white) / 0.1);
  border-top: 5px solid hsl(var(--base));
  border-radius: 12px;
  padding: 25px;
}

.custom--card .card-header {
  padding: 12px 24px;
  background-color: transparent;
  border-bottom: 1px solid hsl(var(--black) / 0.1);
}

.custom--card .card-header .title {
  margin-bottom: 0;
}

.custom--card .card-body {
  padding: 24px;
  border-radius: 5px;
}

.custom--card .card-footer {
  padding: 12px 24px;
  background-color: transparent;
  border-top: 1px solid hsl(var(--black) / 0.1);
}

@media (max-width: 767px) {
  .custom--card {
    padding: 8px;
  }
  .custom--card .card-body {
    padding: 12px;
  }
}

/* ================================= Card Css End =========================== */
/* ================================= Form Css Start =========================== */
.form--label {
  margin-bottom: 10px;
  color: hsl(var(--white) / 0.6);
  font-weight: 400;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group .require {
  color: hsl(var(--danger));
  font-size: 16px;
}

.re-captha {
  margin-top: 24px;
}

.form-submit .btn--gradient {
  padding: 20px 24px !important;
  font-size: 32px;
  border: 0;
  background: hsl(var(--base-d-100));
  border-radius: 16px;
}

@media screen and (max-width: 575px) {
  .form-submit .btn--gradient {
    padding: 16px 24px !important;
    font-size: 28px;
  }
}

@media screen and (max-width: 424px) {
  .form-submit .btn--gradient {
    padding: 14px 20px !important;
    font-size: 24px;
  }
}

.form-submit .btn--gradient::after {
  height: calc(100% - 10px);
  width: calc(100% - 10px);
  background: var(--base-gradient);
  border-top: 0;
  border: 4px solid hsl(var(--base-two));
}

.input-inner {
  display: flex;
  align-items: center;
}

.input-inner.border-style .form--control {
  border: 1px solid hsl(var(--white) / 0.1);
  border-radius: 4px;
}

.input-inner.border-style .form--control:focus {
  border: 1px solid hsl(var(--base));
}

.input-inner.right-style .form--control {
  border-right: 0;
  border-left: 1px solid hsl(var(--white) / 0.1);
  border-radius: 4px 0px 0px 4px;
}

.input-inner.right-style .form--control:focus {
  border-left: 1px solid hsl(var(--base));
}

.input-inner__styletwo .form--control {
  border-radius: 12px 0px 0px 12px !important;
}

.input-inner__styletwo .input-inner__text {
  border-radius: 0px 12px 12px 0px !important;
}

.input-inner__icon,
.input-inner__text {
  height: 56px;
  width: 60px;
  border-radius: 4px 0 0 4px;
  display: grid;
  place-content: center;
  color: hsl(var(--dark));
  flex-shrink: 0;
  background: var(--base-gradient);
  font-size: 24px;
}

@media screen and (max-width: 424px) {
  .input-inner__icon,
  .input-inner__text {
    height: 46px;
    width: 48px;
    font-size: 18px;
  }
}

.input-inner__text {
  width: 92px;
  font-size: 18px;
  font-weight: 700;
}

@media screen and (max-width: 424px) {
  .input-inner__text {
    width: 70px;
  }
}

.input-inner .form--control {
  flex: 1;
  border-left: 0;
  height: 56px;
  border-radius: 0px 4px 4px 0px;
}

@media screen and (max-width: 424px) {
  .input-inner .form--control {
    height: 46px;
  }
}
/* Form Select */
.select {
  color: hsl(var(--black) / 0.6) !important;
}

.select:focus {
  border-color: hsl(var(--base));
  color: hsl(var(--black)) !important;
}

.select option {
  background-color: hsl(var(--black));
  color: hsl(var(--white));
}

/* Form Control Start */
.form--control {
  border-radius: 5px;
  font-weight: 400;
  outline: none;
  width: 100%;
  padding: 13px 24px;
  background-color: hsl(var(--dark)) !important;
  border: 1px solid hsl(var(--white) / 0.1);
  color: hsl(var(--white) / 0.6);
  line-height: 1.28;
}

.form--control::placeholder {
  color: hsl(var(--white) / 0.4);
}

.form--control:focus {
  border-color: hsl(var(--base));
  box-shadow: none;
  color: hsl(var(--white) / 0.6) !important;
}

.form--control:disabled,
.form--control[readonly] {
  background-color: hsl(var(--white) / 0.05) !important;
  opacity: 1;
  border: 1px solid hsl(var(--white) / 0.1);
}

.form--control[type="password"] {
  color: hsl(var(--white) / 0.6);
}

.form--control[type="password"]:focus {
  color: hsl(var(--white) / 0.6);
}

.form--control[type="file"] {
  line-height: 50px;
  padding: 0;
  position: relative;
}

.form--control[type="file"]::file-selector-button {
  border: 1px solid hsl(var(--black) / 0.08);
  padding: 4px 6px;
  border-radius: 0.2em;
  background-color: hsl(var(--base)) !important;
  transition: 0.2s linear;
  line-height: 25px;
  position: relative;
  margin-left: 15px;
  color: hsl(var(--body-color)) !important;
}

.form--control[type="file"]::file-selector-button:hover {
  background-color: hsl(var(--base));
  border: 1px solid hsl(var(--base));
  color: hsl(var(--black));
}

textarea.form--control {
  height: 130px;
}

@media screen and (max-width: 424px) {
  textarea.form--control {
    height: 90px;
  }
}
/* Autofill Css */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
textarea:-webkit-autofill:active {
  -webkit-transition: background-color 5000s ease-in-out 0s;
  transition: background-color 5000s ease-in-out 0s;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill,
textarea:-webkit-autofill,
textarea:-webkit-autofill,
textarea:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset;
  -webkit-text-fill-color: hsl(var(--white)) !important;
  caret-color: hsl(var(--white));
}

/* Autofill Css End */
/* input group */
.input--group {
  position: relative;
}

/* Show Hide Password */
input#your-password,
input#confirm-password {
  padding-right: 50px;
}

.password-show-hide {
  position: absolute;
  right: 20px;
  z-index: 5;
  cursor: pointer;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(var(--white) / 0.6);
}

/* --------------- Number Arrow None --------------------- */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* Custom Checkbox Design */
.form--check a {
  display: inline;
}

.form--check .form-check-input {
  box-shadow: none;
  background-color: transparent;
  box-shadow: none !important;
  border: 0;
  position: relative;
  border-radius: 0px;
  width: 16px;
  height: 16px;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
  border-image: var(--base-gradient);
  border-image-slice: 1;
}

.form--check .form-check-input:checked {
  border-color: hsl(var(--base)) !important;
  box-shadow: none;
}

.form--check .form-check-input:checked[type="checkbox"] {
  background-image: none;
}

.form--check .form-check-input:checked::before {
  position: absolute;
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  background: var(--base-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.form--check .form-check-label {
  font-weight: 600;
  width: calc(100% - 16px);
  padding-left: 10px;
  cursor: pointer;
  color: hsl(var(--white));
}

.form--check .form-check-label .text {
  background: var(--base-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

@media screen and (max-width: 424px) {
  .form--check label {
    font-size: 0.9375rem;
  }
}

@media screen and (max-width: 424px) {
  .form--check a {
    font-size: 0.9375rem;
  }
}
/* Custom Radio Design */
.form--radio .form-check-input {
  box-shadow: none;
  border: 1px solid hsl(var(--black) / 0.2);
  position: relative;
  background-color: transparent;
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.form--radio .form-check-input:active {
  filter: brightness(100%);
}

.form--radio .form-check-input:checked {
  background-color: transparent;
  border-color: hsl(var(--base));
}

.form--radio .form-check-input:checked[type="radio"] {
  background-image: none;
}

.form--radio .form-check-input:checked::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 7px;
  height: 7px;
  background-color: hsl(var(--base));
  border-radius: 50%;
  z-index: 999;
}

.form--radio .form-check-label {
  font-weight: 600;
  width: calc(100% - 16px);
  padding-left: 12px;
  cursor: pointer;
}

/*  Custom Switch Design */
.form--switch .form-check-input {
  border-radius: 3px;
  background-image: none;
  position: relative;
  box-shadow: none;
  border: 0;
  background-color: hsl(var(--white)) !important;
  padding: 10px !important;
  margin-left: 0;
  margin-bottom: 5px;
  border-radius: 40px;
  width: 70px;
  height: 38px;
  cursor: pointer;
}

.form--switch .form-check-input:focus {
  border-radius: 40px;
  background-image: none;
  position: relative;
  box-shadow: none;
  border: 0;
}

.form--switch .form-check-input::before {
  position: absolute;
  content: "";
  width: 28px;
  height: 28px;
  background-color: hsl(var(--white));
  top: 50%;
  transform: translateY(-50%);
  border-radius: 2px;
  left: 5px;
  border-radius: 50%;
  transition: 0.2s linear;
}

.form--switch .form-check-input:checked {
  background-color: hsl(var(--base)) !important;
}

.form--switch .form-check-input:checked::before {
  left: calc(100% - 33px);
  background-color: hsl(var(--white)) !important;
}

.form--switch .form-check-input:checked[type="checkbox"] {
  background-image: none;
}

.form--switch .form-check-label {
  width: calc(100% - 14px);
  padding-left: 5px;
  cursor: pointer;
}

/*  Custom Switch End Design */
/* ================================= Form Css End =========================== */
/* ================================= Modal Css Start =========================== */
.custom--modal.modal {
  background-color: hsl(var(--black) / 0.45) !important;
}

.custom--modal .modal-header {
  border-bottom: 1px solid hsl(var(--base) / 0.3);
}
.custom--modal .modal-header,
.custom--modal .modal-footer {
  padding: 16px;
}

.custom--modal .modal-content {
  background-color: hsl(var(--black)) !important;
  border-radius: 10px !important;
}

.custom--modal .btn-close {
  transition: 0.2s linear;
}

.custom--modal .btn-close:focus {
  box-shadow: none;
}

.custom--modal .btn-close:hover {
  background-color: hsl(var(--black) / 0.08);
  border-radius: 50%;
}

.custom--modal .modal-body {
  padding: 16px;
  background-size: 10px;
}

.custom--modal .modal-icon i {
  font-size: 2rem;
  color: hsl(var(--base));
  border: 3px solid hsl(var(--base));
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.custom--modal .modal-footer {
  border-top: 1px solid hsl(var(--base) / 0.3);
}

/* ================================= Modal Css End =========================== */
/* ================================= Pagination Css Start =========================== */
.pagination {
  flex-wrap: wrap;
  justify-content: center;
}

@media screen and (max-width: 1199px) {
  .pagination {
    margin-top: 48px;
  }
}

@media screen and (max-width: 991px) {
  .pagination {
    margin-top: 40px;
  }
}

@media screen and (max-width: 767px) {
  .pagination {
    margin-top: 32px;
  }
}

.pagination .page-item.active .page-link {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
  border-color: hsl(var(--base));
}

.pagination .page-item .page-link {
  border: 1px solid hsl(var(--black) / 0.15);
  margin: 0 5px;
  border-radius: 5px;
  height: 36px;
  width: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  font-weight: 500;
  padding: 0;
  color: hsl(var(--body-color));
}

.pagination .page-item .page-link:hover {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
  border-color: hsl(var(--base));
}

.pagination .page-item .page-link:focus {
  box-shadow: none;
}

/* ================================= Pagination Css End =========================== */
/* Table Css Start */
.table {
  margin: 0;
  border-collapse: collapse;
  border-collapse: separate;
  border-spacing: 0px 0px;
}

.table thead tr {
  border: 0;
}

.table thead tr th {
  text-align: center;
  padding: 21px 20px;
  color: hsl(var(--dark));
  font-family: var(--heading-font);
  font-weight: 600;
  max-width: 170px;
  font-size: 24px;
  white-space: nowrap;
}

@media screen and (max-width: 1399px) {
  .table thead tr th {
    padding: 10px;
  }
}

.table tbody {
  border: 0 !important;
}

.table tbody tr:last-child {
  border-bottom: 0;
}

.table tbody tr:last-child td {
  border-bottom: 0;
}

.table tbody tr td {
  text-align: center;
  vertical-align: middle;
  padding: 16px 32px;
  border-width: 1px;
  border: 0;
  font-weight: 400;
  max-width: 170px;
  border-bottom: 1px solid hsl(var(--white) / 0.1);
  background-color: hsl(var(--black));
  color: hsl(var(--white) / 0.6);
}

@media screen and (max-width: 575px) {
  .table tbody tr td {
    font-size: 12px;
  }
}

.table tbody tr td::before {
  content: attr(data-label);
  font-family: var(--heading-font);
  font-size: 24px;
  line-height: 0.8;
  background: var(--base-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 500;
  display: none;
  width: 45% !important;
  text-align: left;
}

@media screen and (max-width: 575px) {
  .table tbody tr td::before {
    font-size: 18px;
  }
}

@media screen and (max-width: 767px) {
  .table--responsive--md thead {
    display: none;
  }

  .table--responsive--md tbody tr {
    display: block;
  }

  .table--responsive--md tbody tr:last-child td {
    border-bottom: 0;
  }

  .table--responsive--md tbody tr td {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--black) / 0.8);
    max-width: unset;
  }

  .table--responsive--md tbody tr td:last-child {
    border: none;
  }

  .table--responsive--md tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--md tbody tr td::before {
    display: block;
  }
}

@media screen and (max-width: 767px) {
  .table--responsive--md tbody tr td {
    border: 0;
  }
}

@media screen and (max-width: 991px) {
  .table--responsive--lg thead {
    display: none;
  }

  .table--responsive--lg tbody tr:nth-child(even) {
    background-color: hsl(var(--black) / 0.02);
  }

  .table--responsive--lg tbody tr:last-child td {
    border-bottom: 0;
  }

  .table--responsive--lg tbody tr td {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--white) / 0.1) !important;
    max-width: unset;
  }

  .table--responsive--lg tbody tr td:last-child {
    border: none;
  }

  .table--responsive--lg tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--lg tbody tr td::before {
    display: block;
  }
}

@media screen and (max-width: 991px) {
  .table--responsive--lg tbody tr td {
    border: none;
  }
}

@media screen and (max-width: 1199px) {
  .table--responsive--xl thead {
    display: none;
  }

  .table--responsive--xl tbody tr {
    display: block;
  }

  .table--responsive--xl tbody tr:nth-child(even) {
    background-color: hsl(var(--black) / 0.02);
  }

  .table--responsive--xl tbody tr td {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--black) / 0.08);
    max-width: unset;
  }

  .table--responsive--xl tbody tr td:last-child {
    border: none;
  }

  .table--responsive--xl tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--xl tbody tr td::before {
    display: block;
  }
}

@media screen and (max-width: 1199px) {
  .table--responsive--xl tbody tr td {
    border: 0;
  }
}

@media screen and (max-width: 1399px) {
  .table--responsive--xxl thead {
    display: none;
  }

  .table--responsive--xxl tbody tr {
    display: block;
  }

  .table--responsive--xxl tbody tr:last-child td {
    border-bottom: 0;
  }

  .table--responsive--xxl tbody tr td {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--black) / 0.08);
    max-width: unset;
  }

  .table--responsive--xxl tbody tr td:last-child {
    border: none;
  }

  .table--responsive--xxl tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--xxl tbody tr td::before {
    display: block;
  }
}

@media screen and (max-width: 1399px) {
  .table--responsive--xxl tbody tr td {
    border: 0;
  }
}
/* ================================= Table Css End =========================== */
/* ================================= Tab Css Start =========================== */
.custom--tab {
  justify-content: center;
  border-radius: 6px;
  margin-bottom: 40px !important;
}

.custom--tab .nav-item {
  border-bottom: 0;
  padding: 5px;
}

.custom--tab .nav-item .nav-link {
  color: hsl(var(--base));
  padding: 8px 25px !important;
  background-color: transparent !important;
  border-radius: 5px;
  transition: 0.4s;
  border: 1px solid hsl(var(--black) / 0.08) !important;
}

@media screen and (max-width: 1199px) {
  .custom--tab .nav-item .nav-link {
    padding: 12px 15px !important;
  }
}

.custom--tab .nav-item .nav-link.active {
  color: hsl(var(--white));
  background-color: hsl(var(--base-d-200)) !important;
  border: 1px solid transparent !important;
}

.custom--tab .nav-item .nav-link.active:hover {
  color: hsl(var(--white));
}

.custom--tab .nav-item .nav-link:hover {
  color: hsl(var(--base));
}

/* ================================= Tab Css End =========================== */
/* ================================= Badge Css Start =========================== */
.badge {
  border-radius: 5px;
  padding: 8px 10px;
  font-weight: 500;
  position: relative;
  text-align: center;
}

.badge--base {
  background-color: hsl(var(--base) / 0.15) !important;
  color: hsl(var(--base)) !important;
}

.badge--primary {
  background-color: hsl(var(--primary) / 0.15) !important;
  color: hsl(var(--primary)) !important;
}

.badge--secondary {
  background-color: hsl(var(--secondary) / 0.15) !important;
  color: hsl(var(--secondary)) !important;
}

.badge--success {
  background-color: hsl(var(--success) / 0.15) !important;
  color: hsl(var(--success)) !important;
}

.badge--danger {
  background-color: hsl(var(--danger) / 0.15) !important;
  color: hsl(var(--danger)) !important;
}

.badge--warning {
  background-color: hsl(var(--warning) / 0.15) !important;
  color: hsl(var(--warning)) !important;
}

.badge--info {
  background-color: hsl(var(--info) / 0.15) !important;
  color: hsl(var(--info)) !important;
}

.badge--dark {
  background-color: hsl(var(--dark) / 0.15) !important;
  color: hsl(var(--dark)) !important;
}

/* ================================= Badge Css End =========================== */
/* ====================================== Alert Css Start =============================== */
.alert {
  margin-bottom: 0;
  background-color: hsl(var(--white)) !important;
  font-weight: 400;
  padding: 17px 24px;
  border-radius: 5px;
}

@media screen and (max-width: 991px) {
  .alert {
    padding: 16px;
  }
}

@media screen and (max-width: 575px) {
  .alert {
    padding: 12px;
  }
}

.alert__icon {
  font-size: 1.5rem;
  line-height: 1;
}

.alert__content {
  width: calc(100% - 24px);
  padding-left: 32px;
}

@media screen and (max-width: 991px) {
  .alert__content {
    padding-left: 16px;
  }
}

@media screen and (max-width: 575px) {
  .alert__content {
    padding-left: 0;
    width: 100%;
    margin-top: 6px;
  }
}

.alert__title {
  color: hsl(var(--base-two) / 0.8);
  font-weight: 600;
  font-family: var(--heading-font);
  margin-bottom: 6px;
}

.alert__desc {
  color: hsl(var(--base-two) / 0.5);
  display: block;
  line-height: 1.375;
}

@media screen and (max-width: 424px) {
  .alert__desc {
    font-size: 0.8125rem;
  }
}

.alert__link {
  position: relative;
}

.alert__link:hover::before {
  visibility: visible;
  opacity: 1;
  bottom: 0;
}

.alert__link::before {
  left: 0;
  bottom: -5px;
  width: 100%;
  height: 1px;
  background-color: hsl(var(--base));
  visibility: hidden;
  opacity: 0;
  transition: 0.2s ease-in-out;
}

.alert--base {
  border-color: hsl(var(--base) / 0.6);
}

.alert--base .alert__icon {
  color: hsl(var(--base));
}

.alert--primary {
  border-color: hsl(var(--primary) / 0.6);
}

.alert--primary .alert__icon {
  color: hsl(var(--primary));
}

.alert--success {
  border-color: hsl(var(--success) / 0.6);
}

.alert--success .alert__icon {
  color: hsl(var(--success));
}

.alert--info {
  border-color: hsl(var(--info) / 0.6);
}

.alert--info .alert__icon {
  color: hsl(var(--info));
}

.alert--danger {
  border-color: hsl(var(--danger) / 0.6);
}

.alert--danger .alert__icon {
  color: hsl(var(--danger));
}

.alert--warning {
  border-color: hsl(var(--warning) / 0.6);
}

.alert--warning .alert__icon {
  color: hsl(var(--warning));
}

.alert--secondary {
  border-color: hsl(var(--secondary) / 0.6);
}

.alert--secondary .alert__icon {
  color: hsl(var(--secondary));
}

.alert--dark {
  border-color: hsl(var(--dark) / 0.6);
}

.alert--dark .alert__icon {
  color: hsl(var(--dark));
}

/* ====================================== Alert Css End =============================== */
/* ================================= preload Css Start =========================== */
.preloader {
  position: fixed;
  z-index: 999999;
  background-color: hsl(var(--black));
  width: 100%;
  height: 100%;
}

.loader-p {
  border: 0 solid transparent;
  border-radius: 50%;
  width: 150px;
  height: 150px;
  position: absolute;
  top: calc(50vh - 75px);
  left: calc(50vw - 75px);
}

.loader-p:before,
.loader-p:after {
  content: "";
  border: 1em solid hsl(var(--base));
  border-radius: 50%;
  width: inherit;
  height: inherit;
  position: absolute;
  top: 0;
  left: 0;
  animation: loader 2s linear infinite;
  opacity: 0;
}

.loader-p:before {
  animation-delay: 0.5s;
}

@keyframes loader {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}
/* ================================= preload Css End ===========================  */
/* ============= Header Start Here ======================= */
.navbar-brand {
  padding-top: 0;
  padding-bottom: 0;
}

.navbar-brand.logo img {
  max-width: 200px;
  max-height: 60px;
}

@media screen and (max-width: 1199px) {
  .navbar-brand.logo img {
    max-width: 140px;
  }
}

.header {
  position: relative;
  z-index: 5;
  background: hsl(var(--dark) / 0.7);
  position: absolute;
  width: 100%;
}

@media screen and (max-width: 1199px) {
  .header {
    top: 0px;
    padding: 10px 0;
    position: absolute;
    left: 0;
    right: 0;
    z-index: 999;
    background: hsl(var(--dark));
  }

  .header::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  .header::-webkit-scrollbar-thumb {
    border-radius: 0px;
  }
}

.header.fixed-header {
  position: fixed;
  transition: 0.3s linear;
  top: 0px;
  background: hsl(var(--dark));
  animation: slide-down 0.8s;
  width: 100%;
  box-shadow: 0 0 20px hsl(var(--base) / 0.2);
}

@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-150%);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.navbar {
  padding: 0 !important;
}

/* ========================= Desktop Device Start ========================= */
@media (min-width: 1199px) {
  .header .nav-menu {
    padding-top: 0;
    padding-bottom: 0;
  }

  .header .nav-menu .nav-item {
    position: relative;
    padding-right: 24px;
  }

  .header .nav-menu .nav-item:last-child {
    padding-right: 0;
  }

  .header .nav-menu .nav-item.active .nav-link {
    color: hsl(var(--base)) !important;
  }

  .header .nav-menu .nav-item.active .nav-link::before {
    width: 100%;
  }

  .header .nav-menu .nav-item .nav-link.active {
    background: linear-gradient(
      270deg,
      hsl(var(--base-two)) -13.66%,
      hsl(var(--base)) 90.62%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .header .nav-menu .nav-item.active .nav-link {
    background: linear-gradient(
      270deg,
      hsl(var(--base-two)) -13.66%,
      hsl(var(--base)) 90.62%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .header .nav-menu .nav-item:hover .nav-link::before {
    width: 100%;
  }

  .header .nav-menu .nav-item:hover .nav-link .nav-item__icon {
    transform: rotate(180deg);
    transition: 0.2s;
  }

  .header .nav-menu .nav-item .nav-link {
    font-weight: 700;
    font-size: 22px;
    color: hsl(var(--white)) !important;
    padding: 33px 0;
    position: relative;
    cursor: pointer;
    font-family: var(--heading-font);
    /* ======================== Style two ================ */
  }

  .header .nav-menu .nav-item .nav-link:hover::before {
    left: 0;
    transition: 0.3s;
  }

  .header .nav-menu .nav-item .nav-link::before {
    position: absolute;
    content: "";
    right: 0;
    bottom: 30px;
    width: 0;
    height: 2px;
    background: var(--base-gradient);
    transition: 0.3s;
  }

  .header .nav-menu .nav-item .nav-link .nav-item__icon {
    transition: 0.3s;
    font-size: 0.8125rem;
    margin-left: 2px;
  }
}

@media screen and (min-width: 1199px) and (max-width: 1199px) {
  .header .nav-menu .nav-item .nav-link .nav-item__icon {
    margin-right: 6px;
  }
}

@media (min-width: 1199px) {
  .header .nav-menu .nav-item:hover .dropdown-menu {
    visibility: visible;
    opacity: 1;
    top: 100% !important;
    transform: scaleY(1);
  }
}
/* ========================== Desktop Device End ========================= */
/* ============================== Small Device ======================= */
@media screen and (max-width: 1199px) {
  .header .nav-menu {
    margin-top: 20px;
  }

  .header .nav-menu .nav-item {
    text-align: left;
    display: block;
    position: relative;
    margin: 0;
  }

  .header .nav-menu .nav-item:hover .nav-link .nav-item__icon {
    transform: rotate(0deg) !important;
  }

  .header .nav-item:first-child {
    border-bottom: none;
  }

  .header .nav-item:last-child > a {
    border-bottom: 0;
  }

  .header .nav-item.active .nav-link {
    color: hsl(var(--base));
  }

  .header .nav-item .nav-link {
    margin-bottom: 8px;
    padding: 10px 10px 10px 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 !important;
    border-bottom: 1px solid hsl(var(--base-two) / 0.4);
    color: hsl(var(--white));
    font-weight: 700;
    font-size: 24px;
    cursor: pointer;
    font-family: var(--heading-font);
  }

  .header .nav-item .nav-link::before {
    display: none;
  }

  .header .nav-item .nav-link.show[aria-expanded="true"] {
    color: hsl(var(--base)) !important;
  }

  .header .nav-item .nav-link.show[aria-expanded="true"] i {
    transform: rotate(180deg);
  }
}

.header .navbar-toggler.header-button {
  border-color: transparent;
  color: hsl(var(--white));
  background: transparent !important;
  padding: 0 !important;
  border: 0 !important;
  border-radius: 0 !important;
  transition: 0.15s ease-in-out;
  width: auto;
  font-size: 2.5rem;
}

.header .navbar-toggler.header-button:focus {
  box-shadow: none !important;
}

.header .navbar-toggler.header-button[aria-expanded="true"] i::before {
  content: "\f00d";
}

@media screen and (max-width: 1199px) {
  .body-overlay.show {
    visibility: visible;
    opacity: 1;
  }
}

.language {
  display: flex;
  align-items: center;
  gap: 10px;
}

.language__icon {
  height: 45px;
  width: 45px;
  border-radius: 50%;
  background: linear-gradient(
    180deg,
    hsl(var(--base)) 5.48%,
    hsl(var(--base-two)) 59.04%,
    hsl(var(--base)) 107.63%
  );
  border: 2px solid hsl(var(--base-two));
  color: hsl(var(--dark));
  display: grid;
  place-content: center;
  font-size: 24px;
}

.language__wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.language__wrapper.show .language__arrow {
  transform: rotate(180deg);
}

.language__text {
  color: hsl(var(--white));
  font-size: 24px;
  font-weight: 700;
  font-family: var(--heading-font);
}

.language__arrow {
  font-size: 14px;
  color: hsl(var(--white));
  transition: all linear 0.2s;
}

.language .dropdown-menu {
  position: absolute;
  transition: ease-in-out 0.1s;
  opacity: 0;
  visibility: hidden;
  top: 100%;
  display: unset;
  background: hsl(var(--dark));
}

.language .dropdown-menu.show {
  visibility: visible;
  opacity: 1;
}

.language .dropdown-menu .language_text {
  color: hsl(var(--white));
  padding: 0px 16px;
  font-family: var(--heading-font);
  font-size: 24px;
  cursor: pointer;
  font-weight: 600;
}

/* ================================= Header Css End =========================== */
/* ============= Footer Start Here ======================= */
.footer-area {
  margin-top: 250px;
  position: relative;
}

@media screen and (max-width: 1399px) {
  .footer-area {
    margin-top: 220px;
  }
}

@media screen and (max-width: 767px) {
  .footer-area {
    margin-top: 170px;
  }
}

@media screen and (max-width: 575px) {
  .footer-area {
    margin-top: 120px;
  }
}

.footer-area::after,
.footer-area::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  width: 100%;
  left: 0;
}

.footer-area::after {
  -webkit-mask-image: url(../images/shapes/footer-bg.png);
  background-image: url(../images/shapes/footer-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  height: 100%;
  top: 0;
  z-index: -1;
}

.footer-area::before {
  -webkit-mask-image: url(../images/shapes/footer-shape.png);
  height: calc(100% + 10px);
  top: -20px;
  z-index: -2;
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
}

.footer-area__thumb {
  position: relative;
  z-index: -3;
  margin: calc(-10% + 10px) auto -40px;
  max-width: 1120px;
}

@media screen and (max-width: 1399px) {
  .footer-area__thumb {
    margin: -15% auto -70px;
  }
}

@media screen and (max-width: 1199px) {
  .footer-area__thumb {
    margin: -200px auto -100px;
  }
}

@media screen and (max-width: 991px) {
  .footer-area__thumb {
    position: absolute;
    top: 7%;
  }
}

@media screen and (max-width: 767px) {
  .footer-area__thumb {
    position: absolute;
    top: 10%;
  }
}

@media (max-width: 660px) {
  .footer-area__thumb {
    position: absolute;
    top: 15%;
  }
}

@media (max-width: 400px) {
  .footer-area__thumb {
    position: absolute;
    top: 20%;
  }
}

.payment-method {
  border-bottom: 1px solid hsl(var(--white) / 0.1);
}

@media screen and (max-width: 1199px) {
  .payment-method {
    padding-bottom: 50px;
  }
}

@media screen and (max-width: 767px) {
  .payment-method {
    padding-bottom: 30px;
  }
}

.payment-method__title {
  text-align: center;
  margin-bottom: 32px;
}

@media screen and (max-width: 991px) {
  .payment-method {
    padding-top: 100px;
  }
}

.payment-slider .slick-slide {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media screen and (max-width: 767px) {
  .payment-slider .slick-slide {
    display: block !important;
  }
}

.payment-single-item {
  -webkit-mask-image: url(../images/shapes/payment-item.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  background-image: url(../images/shapes/payment-item.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  width: 208px !important;
  height: 108px;
  display: grid !important;
  place-content: center;
}

@media screen and (max-width: 767px) {
  .payment-single-item {
    background-image: none !important;
    border-radius: 12px;
    width: 100% !important;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    margin: 10px;
    -webkit-mask-image: none !important;
  }
}

.footer-bottom__wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

@media screen and (max-width: 991px) {
  .footer-bottom__wrapper {
    flex-direction: column;
  }
}

.footer-bottom__logo {
  flex-shrink: 0;
}

.footer-bottom__logo img {
  width: 240px;
}

.newsletter {
  max-width: 670px;
  position: relative;
  flex: 1;
}

@media screen and (max-width: 991px) {
  .newsletter {
    width: 100%;
  }
}

.newsletter__wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.newsletter__input {
  flex: 1;
}

.newsletter__input input {
  height: 70px;
  border-radius: 8px 0px 0px 8px;
  border: 1px solid hsl(var(--dark) / 0.1) !important;
  background: hsl(var(--dark)) !important;
  color: hsl(var(--light));
}

@media screen and (max-width: 767px) {
  .newsletter__input input {
    height: 60px;
  }
}

@media screen and (max-width: 424px) {
  .newsletter__input input {
    height: 50px;
  }
}

.newsletter__input input::placeholder {
  color: hsl(var(--white) / 0.2);
}

.newsletter__btn {
  flex-shrink: 0;
  padding: 0 50px;
  height: 70px;
  border-radius: 0px 8px 8px 0px;
  background: var(--base-gradient);
  color: hsl(var(--dark));
  font-weight: 700;
  font-size: 24px;
  font-family: var(--heading-font);
}

@media screen and (max-width: 767px) {
  .newsletter__btn {
    height: 60px;
    padding: 0px 25px;
  }
}

@media screen and (max-width: 424px) {
  .newsletter__btn {
    font-size: 18px;
    height: 50px;
    padding: 0px 15px;
  }
}

.socket-area {
  background-color: #303a45;
  padding: 16px 0;
}

.socket-area__wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px 12px;
}

@media screen and (max-width: 575px) {
  .socket-area__wrapper {
    flex-direction: column;
  }
}

.socket-nav {
  gap: 32px;
}

@media screen and (max-width: 991px) {
  .socket-nav {
    gap: 16px;
  }
}

.socket-nav__link {
  color: hsl(var(--white) / 0.4);
}

.socket-nav__link:hover {
  color: hsl(var(--base));
  text-decoration: underline;
}

/* ===================== Scroll to Top Start ================================= */
.scroll-top {
  position: fixed;
  right: 30px;
  bottom: 30px;
  color: hsl(var(--black));
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  z-index: 5;
  transition: 0.5s;
  cursor: pointer;
  transform: scale(0);
  background: var(--base-gradient);
}

.scroll-top:hover {
  color: hsl(var(--black));
}

.scroll-top.show {
  transform: scale(1);
}

/* ===================== Scroll to Top End ================================= */
/* ================================= Template Selection Css Start =========================== */
::selection {
  color: hsl(var(--white));
  background: hsl(var(--base-d-100));
}

/* ================================= Template Selection Css End ===========================  */
/* ================================= Social Icon Css Start =========================== */
.social-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.social-list__item {
  margin-right: 10px;
}

.social-list__item:last-child {
  margin-right: 0;
}

.social-list__link {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  color: hsl(var(--white) / 0.4);
  background-color: #303a45;
  border: 2px solid rgba(255, 255, 255, 0.1);
  z-index: 1;
  transition: all linear 0.3s;
}

.social-list__link::after {
  content: "";
  position: absolute;
  height: calc(100% + 4px);
  width: calc(100% + 4px);
  background: var(--base-gradient);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  transition: all linear 0.3s;
  opacity: 0;
  border-radius: 50%;
}

.social-list__link.active,
.social-list__link:hover,
.social-list__link:focus {
  color: hsl(var(--dark)) !important;
}

.social-list__link.active::after,
.social-list__link:hover::after,
.social-list__link:focus::after {
  opacity: 1;
}

@media screen and (max-width: 767px) {
  .social-list__link {
    width: 35px;
    height: 35px;
    font-size: 0.875rem;
  }
}
/* ================================= Social Icon Css End ===========================  */
/* ====================== Breadcrumb Css Start ==================== */
.breadcrumb {
  margin-bottom: 0;
  position: relative;
  z-index: 1;
  padding: 175px 0 166px;
}

@media screen and (max-width: 1599px) {
  .breadcrumb {
    padding: 160px 0 90px;
  }
}

@media screen and (max-width: 1399px) {
  .breadcrumb {
    padding: 140px 0 60px;
  }
}

@media screen and (max-width: 1199px) {
  .breadcrumb {
    padding: 120px 0 70px;
  }
}

@media screen and (max-width: 991px) {
  .breadcrumb {
    padding: 110px 0 60px;
  }
}

@media screen and (max-width: 767px) {
  .breadcrumb {
    padding: 80px 0 50px;
  }
}

.breadcrumb::after,
.breadcrumb::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  left: 0;
}

.breadcrumb::after {
  -webkit-mask-image: url(../images/shapes/breadcumb-mask.png);
  background-image: url(../images/shapes/breadcumb-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
  z-index: -2;
  top: 0;
  width: 100%;
  height: 100%;
}

.breadcrumb::before {
  -webkit-mask-image: url(../images/shapes/breadcumb-shape.png);
  z-index: -2;
  background: linear-gradient(
    88deg,
    hsl(var(--base-two)) 13.34%,
    hsl(var(--base)) 78.66%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  width: 100%;
  height: calc(100% + 10px);
  top: 10px;
}

@media screen and (max-width: 575px) {
  .breadcrumb::before {
    top: 0;
  }
}

.breadcrumb__overlay {
  -webkit-mask-image: url(../images/shapes/breadcumb-overlay.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  z-index: -1;
}

.breadcrumb__overlay img {
  width: 100%;
  height: calc(100% + 10px);
}

.breadcrumb__wrapper {
  text-align: center;
}

.breadcrumb__title {
  margin-bottom: 24px;
}

@media screen and (max-width: 1199px) {
  .breadcrumb__title {
    margin-bottom: 16px;
  }
}

@media screen and (max-width: 991px) {
  .breadcrumb__title {
    margin-bottom: 12px;
  }
}

.breadcrumb__list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.breadcrumb__item {
  color: hsl(var(--white));
  padding: 0 5px;
  font-weight: 700;
}

.breadcrumb__item-text {
  color: hsl(var(--white));
}

.breadcrumb__link {
  color: hsl(var(--white));
}

.breadcrumb__link:hover {
  color: hsl(var(--base));
}

/* ====================== Breadcrumb Css End ==================== */
.transection-section .latest-transection {
  padding: 35px 50px 16px;
  border: 1px solid hsl(var(--white) / 0.1);
  border-top: 5px solid hsl(var(--base));
}

@media screen and (max-width: 991px) {
  .transection-section .latest-transection {
    padding: 30px 20px;
  }
}

.transection-section .latest-transection::after {
  -webkit-mask-image: url(../images/shapes/table-bigbg.png);
}

.transection-section .latest-transection::before {
  -webkit-mask-image: url(../images/shapes/table-bigshape.png);
  bottom: 5px;
  left: 11px;
  height: calc(100% + 10px);
  width: calc(100% + 5px);
  z-index: -1;
}

.transection-section .table thead tr th {
  padding: 20px;
}

.blog-sidebar-wrapper {
  position: sticky;
  top: 100px;
}

/* ============ Sidebar search box ============= */
.search-box {
  position: relative;
}

.search-box__button {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(var(--base));
}

/* ================== Sidebar Box & Title =================== */
.blog-sidebar {
  background-color: hsl(var(--black) / 0.03);
  padding: 30px 20px;
  border-radius: 5px;
  margin-bottom: 30px;
}

.blog-sidebar:last-of-type {
  margin-bottom: 0;
}

@media screen and (max-width: 575px) {
  .blog-sidebar {
    padding: 20px 15px;
  }
}

.blog-sidebar__title {
  position: relative;
  padding-bottom: 10px;
  color: hsl(var(--white));
  margin-bottom: 10px;
}

.blog-sidebar__title::before {
  position: absolute;
  content: "";
  width: 60px;
  height: 2px;
  background-color: hsl(var(--base));
  left: 0;
  bottom: 0px;
}

/* ========================= Category & Tags List Style ========================= */
.text-list {
  /* Category */
  /* Style Tag */
}

.text-list.style-category .text-list__item {
  border-bottom: 1px dashed hsl(var(--black) / 0.2);
}

.text-list.style-category .text-list__item:last-child {
  border-bottom: 0;
}

.text-list.style-category .text-list__link {
  color: hsl(var(--body-color));
}

.text-list.style-category .text-list__link:hover {
  color: hsl(var(--base));
}

.text-list.style-tag {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: -7px;
}

.text-list.style-tag .text-list__item {
  padding: 7px;
}

.text-list.style-tag .text-list__link {
  color: hsl(var(--light));
  border: 1px solid hsl(var(--white) / 0.1);
  padding: 6px 20px;
  border-radius: 3px;
}

.text-list.style-tag .text-list__link.active {
  background-color: hsl(var(--base));
  border-color: hsl(var(--base));
  color: hsl(var(--white));
}

.text-list.style-tag .text-list__link:hover {
  background-color: hsl(var(--base));
  border-color: hsl(var(--base));
  color: hsl(var(--white));
}

/* ======================== Latest Blog Css========================  */
.latest-blog {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid hsl(var(--black) / 0.1);
}

.latest-blog:last-of-type {
  padding-bottom: 0px;
  margin-bottom: 0px;
  border-bottom: 0;
}

.latest-blog__thumb {
  width: 80px;
  max-height: 80px;
  display: flex;
  border-radius: 5px;
  overflow: hidden;
}

@media screen and (max-width: 424px) {
  .latest-blog__thumb {
    width: 60px;
  }
}

.latest-blog__thumb a {
  display: block;
  height: 100%;
  width: 100%;
}

.latest-blog__content {
  width: calc(100% - 80px);
  padding-left: 15px;
}

@media screen and (max-width: 424px) {
  .latest-blog__content {
    width: calc(100% - 60px);
  }
}

.latest-blog__title {
  margin-bottom: 5px;
}

.latest-blog__title a {
  color: hsl(var(--heading-color));
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.latest-blog__title a:hover {
  color: hsl(var(--base));
}

/* ========================= Latest Blog Css End ===================*/
/* ================================= Range Slider Css Start =========================== */
.custom--range__content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 10px;
}

.custom--range__content label {
  width: 40%;
}

.custom--range__content input {
  width: 60%;
  text-align: right;
}

.custom--range input {
  border: 0;
  color: hsl(var(--body-color));
  font-weight: 500;
}

.custom--range #slider-range {
  height: 5px;
  border: 0;
  background: hsl(var(--base) / 0.2);
}

.custom--range #slider-range .ui-widget-header {
  background-color: hsl(var(--base));
}

.custom--range #slider-range span:focus {
  background-color: hsl(var(--base));
}

.custom--range #slider-range .ui-slider-handle {
  width: 15px !important;
  height: 15px !important;
  background-color: hsl(var(--base)) !important;
  border: 2px solid hsl(var(--white)) !important;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
}

/* ================================= Range Slider Css End =========================== */
/* ================= Slick Arrow & Dots css Start ================ */
.slick-initialized.slick-slider .slick-list {
  margin: 0 -10px;
}

.slick-initialized.slick-slider .slick-track {
  display: flex;
}

.slick-initialized.slick-slider .slick-slide {
  height: auto;
  padding: 0 10px;
}

.slick-initialized.slick-slider .slick-slide > div {
  height: 100%;
}

.slick-arrow {
  position: absolute;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  color: hsl(var(--white));
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: 0.2s linear;
  background-color: hsl(var(--base));
  color: hsl(var(--white));
  font-size: 20px;
}

@media screen and (max-width: 991px) {
  .slick-arrow {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
}

@media screen and (max-width: 575px) {
  .slick-arrow {
    width: 45px;
    height: 45px;
  }
}

.slick-arrow:hover {
  border-color: hsl(var(--base));
  background-color: hsl(var(--base-two));
}

.slick-next {
  right: -20px;
}

@media screen and (max-width: 991px) {
  .slick-next {
    right: -10px;
  }
}

@media screen and (max-width: 575px) {
  .slick-next {
    right: 10px;
  }
}

.slick-prev {
  left: -20px;
}

@media screen and (max-width: 991px) {
  .slick-prev {
    left: -10px;
  }
}

@media screen and (max-width: 575px) {
  .slick-prev {
    left: 10px;
  }
}
/* -------------- Slick Slider Arrow Style two ------------  */
.slick-arrow {
  position: absolute;
  z-index: 1;
  border: none;
  background-color: transparent;
  color: hsl(var(--white));
  width: 32px;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  transition: 0.4s;
  background-color: hsl(var(--black));
  color: hsl(var(--white));
  top: -67px;
}

@media screen and (max-width: 1199px) {
  .slick-arrow {
    top: -69px;
  }
}

@media screen and (max-width: 767px) {
  .slick-arrow {
    top: auto;
    bottom: -50px;
    right: 50%;
  }
}

.slick-arrow:hover {
  background-color: hsl(var(--black));
  color: hsl(var(--white));
}

.slick-next {
  right: 10px;
  background-color: hsl(var(--base));
}

@media screen and (max-width: 767px) {
  .slick-next {
    top: auto;
    right: calc(50% - 37px);
  }
}

.slick-prev {
  right: 52px;
}

@media screen and (max-width: 767px) {
  .slick-prev {
    top: auto;
    right: calc(50% + 5px);
  }
}
/* Dots Css Start */
.slick-dots {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 50px;
}

@media screen and (max-width: 991px) {
  .slick-dots {
    padding-top: 30px;
  }
}

.slick-dots li {
  display: flex;
}

.slick-dots li button {
  border: none;
  background-color: #d9d9d9;
  color: hsl(var(--white));
  margin: 0 3px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  text-indent: -9999px;
  transition: linear 0.3s;
}

.slick-dots li.slick-active button {
  background: hsl(var(--base-two));
  transform: scale(1.1);
}

/* Dots Css End */
/* ================= Slick Arrow & Dots css Start ================ */
/* =========================== Banner Section Start Here ========================= */
.banner-section {
  position: relative;
  z-index: 1;
  padding: 140px 0 20px;
}

@media screen and (max-width: 1199px) {
  .banner-section {
    padding: 120px 0 60px;
  }
}

@media screen and (max-width: 991px) {
  .banner-section {
    padding: 70px 0 40px;
  }
}

@media screen and (max-width: 991px) {
  .banner-section.bg-img {
    background-position: center right;
    object-fit: contain;
  }
}

.banner-section::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: linear-gradient(
    178deg,
    hsl(var(--dark) / 0.5) 26.04%,
    hsl(var(--dark)) 76.18%
  );
  z-index: -1;
}

.banner-content {
  flex: 1;
}

@media screen and (max-width: 424px) {
  .banner-content {
    text-align: center;
  }
}

.banner-content__title {
  color: hsl(var(--white));
  margin-bottom: 14px;
  line-height: 52px;
  font-size: 58px;
}

@media screen and (max-width: 1399px) {
  .banner-content__title {
    margin-bottom: 10px;
    line-height: 36px;
    font-size: 38px;
  }
}

@media screen and (max-width: 575px) {
  .banner-content__title {
    line-height: 27px;
    font-size: 28px;
  }
}

.banner-content__title .title-inner {
  background: linear-gradient(
    270deg,
    hsl(var(--base-two)) 56.78%,
    hsl(var(--base)) 92.71%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.banner-content__title .title-inner::selection {
  background: linear-gradient(
    270deg,
    hsl(var(--base-two)) 56.78%,
    hsl(var(--base)) 92.71%
  );
}

.banner-content__desc {
  font-size: 20px;
}

@media screen and (max-width: 1199px) {
  .banner-content__desc {
    font-size: 16px;
  }
}

@media screen and (max-width: 575px) {
  .banner-content__desc {
    font-size: 14px;
  }
}

.banner-content__button {
  margin-top: 46px;
}

@media screen and (max-width: 1399px) {
  .banner-content__button {
    margin-top: 30px;
  }
}

@media screen and (max-width: 575px) {
  .banner-content__button {
    margin-top: 15px;
  }
}

.banner-thumb {
  text-align: center;
}

@media screen and (max-width: 991px) {
  .banner-thumb img {
    max-width: 400px;
    width: 100%;
  }
}

.banner-content-slider {
  position: relative;
}

@media screen and (max-width: 424px) {
  .banner-content-slider {
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-radius: 12px;
  }
}

.banner-content-slider::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 60px;
  filter: blur(25px);
  background: linear-gradient(
    256deg,
    rgba(198, 151, 5, 0.7) 23.99%,
    rgba(255, 194, 0, 0.7) 109.47%
  );
  border-radius: 100px 10px 100px 10px;
}

@media screen and (max-width: 424px) {
  .banner-content-slider::before {
    display: none;
  }
}

.banner-content-slider__inner {
  padding: 57px 45px;
  position: relative;
  z-index: 1;
}

@media screen and (max-width: 1399px) {
  .banner-content-slider__inner {
    padding: 30px 25px;
  }
}

@media screen and (max-width: 991px) {
  .banner-content-slider__inner {
    padding: 60px 40px;
  }
}

@media screen and (max-width: 767px) {
  .banner-content-slider__inner {
    padding: 30px 15px;
  }
}

.banner-content-slider__inner::after,
.banner-content-slider__inner::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  height: 100%;
}

@media screen and (max-width: 424px) {
  .banner-content-slider__inner::after,
  .banner-content-slider__inner::before {
    display: none;
  }
}

.banner-content-slider__inner::before {
  width: calc(100% + 10px);
  top: 7px;
  left: 50%;
  transform: translateX(-50%);
  -webkit-mask-image: url(../images/thumbs/banner-shape.png);
  z-index: -2;
  background: linear-gradient(
    259deg,
    hsl(var(--base)) 1.66%,
    hsl(var(--base-two)) 109.25%
  );
}

.banner-content-slider__inner::after {
  width: 100%;
  -webkit-mask-image: url(../images/thumbs/banner-shape.png);
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.banner-slider-item__wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 5px;
}

@media screen and (max-width: 424px) {
  .banner-slider-item__wrapper {
    flex-direction: column-reverse;
  }
}

.banner-slider-item .banner-image {
  width: 185px;
  height: 215px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

@media screen and (max-width: 1399px) {
  .banner-slider-item .banner-image {
    height: 185px;
    width: 160px;
  }
}

@media screen and (max-width: 1199px) {
  .banner-slider-item .banner-image {
    height: 165px;
    width: 140px;
  }
}

@media screen and (max-width: 991px) {
  .banner-slider-item .banner-image {
    height: 200px;
    width: 175px;
  }
}

@media screen and (max-width: 767px) {
  .banner-slider-item .banner-image {
    height: 165px;
    width: 140px;
  }
}

@media screen and (max-width: 575px) {
  .banner-slider-item .banner-image {
    height: 145px;
    width: 130px;
  }
}

.banner-slider-item .banner-image img {
  -webkit-mask-image: url(../images/thumbs/slider-shape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

.banner-slider-item .banner-image::after {
  content: "";
  position: absolute;
  -webkit-mask-image: url(../images/thumbs/slider-shape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  width: calc(100% + 7px);
  height: calc(100% + 7px);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(
    0deg,
    hsl(var(--base)) -6.03%,
    hsl(var(--base-two)) 113.15%
  );
  z-index: -1;
}

/* =========================== Banner Section End Here ========================= */
.games-section-content {
  padding: 86px 44px;
  position: relative;
  z-index: 1;
}

.games-section-inner {
  padding: 100px 50px;
  position: relative;
  background-color: hsl(var(--black));
  border: 1px solid hsl(var(--white) / 0.1);
  border-top: 5px solid hsl(var(--base));
  border-radius: 12px;
}

@media screen and (max-width: 1199px) {
  .games-section-content {
    background-color: hsl(var(--black));
    border-radius: 12px;
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
  }

  .games-section-inner,
  .blog-section-content {
    padding: 80px 40px !important;
  }
}

@media screen and (max-width: 1199px) {
  .games-section-content {
    padding: 80px 30px;
  }
}

@media screen and (max-width: 991px) {
  .games-section-content {
    padding: 40px 25px;
  }

  .games-section-inner,
  .blog-section-content {
    padding: 40px 25px !important;
  }
}

.games-section-content::after,
.games-section-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

@media screen and (max-width: 1199px) {
  .games-section-content::after,
  .games-section-content::before {
    display: none;
  }
}

.games-section-content::after {
  -webkit-mask-image: url(../images/games/game-shape.png);
  background-image: url(../images/games/game-shape.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  top: 0;
  left: 0;
  z-index: -1;
}

.games-section-content::before {
  -webkit-mask-image: url(../images/games/games-shape.png);
  top: -20px;
  left: 0px;
  z-index: -2;
  background: linear-gradient(
    78deg,
    hsl(var(--base-two)) 23.38%,
    hsl(var(--base)) 77.89%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
}

.games-section-wrapper {
  display: flex;
  align-items: stretch;
  justify-content: center;
  flex-wrap: wrap;
  gap: 50px 40px;
}

@media screen and (max-width: 1199px) {
  .games-section-wrapper {
    gap: 40px 30px;
  }
}

@media screen and (max-width: 424px) {
  .games-section-wrapper {
    gap: 30px 20px;
  }
}

.game-item {
  width: calc(25% - 30px);
}

@media screen and (max-width: 1199px) {
  .game-item {
    width: calc(33.3333333333% - 20px);
  }
}

@media screen and (max-width: 767px) {
  .game-item {
    width: calc(50% - 15px);
  }
}

.game-item:hover .game-item__play {
  opacity: 1;
  visibility: visible;
}

.game-item:hover .game-item__play .icon {
  transform: scale(1);
}

.game-item__play {
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: hsl(var(--dark) / 0.8);
  top: 0;
  left: 0;
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: all linear 0.3s;
  visibility: hidden;
  opacity: 0;
}

.game-item__play .btn--sm{
  font-size: 20px;
}

@media (max-width: 991px) {
  .game-item__play{
    gap: 8px;
  }

  .game-item__play .btn--sm{
    font-size: 18px !important;
    padding: 7px 16px !important;
  }
}

@media (max-width: 575px) {
  .game-item__play{
    flex-direction: column;
  }
}

.game-item__play .icon {
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
  width: 48px;
  height: 48px;
  display: grid;
  place-content: center;
  font-size: 18px;
  color: hsl(var(--dark));
  border-radius: 50%;
  transition: all linear 0.3s;
  transform: scale(0.3);
}

.game-item__image {
  position: relative;
  z-index: 1;
}

.game-item__image img {
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  width: 100%;
  height: 330px;
  object-fit: cover;
}

@media screen and (max-width: 1399px) {
  .game-item__image img {
    height: 280px;
  }
}

@media screen and (max-width: 1199px) {
  .game-item__image img {
    height: 315px;
  }
}

@media screen and (max-width: 991px) {
  .game-item__image img {
    height: 228px;
  }
}

@media screen and (max-width: 767px) {
  .game-item__image img {
    height: 252px;
  }
}

@media (max-width: 575px) {
  .game-item__image img {
    height: 240px;
  }
}

@media (max-width: 480px) {
  .game-item__image img {
    height: 210px;
  }
}

@media (max-width: 425px) {
  .game-item__image img {
    height: 170px;
  }
}

@media (max-width: 375px) {
  .game-item__image img {
    height: 126px;
  }
}

.game-item__image::after {
  position: absolute;
  content: "";
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  width: calc(100% + 10px);
  height: calc(100% + 10px);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(
    0deg,
    hsl(var(--base)) -6.03%,
    hsl(var(--base-two)) 113.15%
  );
  z-index: -1;
}

.game-item__title {
  text-align: center;
  margin-top: 30px;
}

@media screen and (max-width: 991px) {
  .game-item__title {
    margin-top: 15px;
  }
}

.games-callaction {
  margin-top: 70px;
  max-width: 1034px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.games-callaction__wrapper {
  padding: 25px 28px;
  background-color: hsl(var(--black)) !important;
  border-radius: 12px;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center right !important;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px 50px;
}

@media screen and (max-width: 991px) {
  .games-callaction__wrapper {
    background-image: none;
  }
}

.games-callaction::after {
  position: absolute;
  content: "";
  height: calc(100% + 6px);
  width: calc(100% + 6px);
  top: 50%;
  left: 50%;
  border-radius: 12px;
  transform: translate(-50%, -50%);
  background: var(--base-gradient);
  z-index: -1;
}

.games-callaction__content {
  max-width: 530px;
  width: 100%;
}

.games-callaction__title {
  margin-bottom: 10px;
}

.games-callaction__desc {
  font-weight: 600;
}

.choose-us-wrapper {
  position: relative;
  padding: 112px 40px 80px;
}

@media screen and (max-width: 991px) {
  .choose-us-wrapper {
    padding: 80px 40px;
  }
}

@media screen and (max-width: 1199px) {
  .choose-us-wrapper {
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
  }
}

@media screen and (max-width: 767px) {
  .choose-us-wrapper {
    padding: 60px 24px;
  }
}

.choose-us-wrapper::after,
.choose-us-wrapper::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  top: 0;
  left: 0;
}

@media screen and (max-width: 1199px) {
  .choose-us-wrapper::after,
  .choose-us-wrapper::before {
    display: none;
  }
}

.choose-us-wrapper::after {
  -webkit-mask-image: url(../images/thumbs/choose-us-bg.png);
  background-image: url(../images/thumbs/choose-us-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
  z-index: -1;
  width: 100%;
  height: 100%;
}

.choose-us-wrapper::before {
  -webkit-mask-image: url(../images/thumbs/choose-us-shape.png);
  z-index: -2;
  background: linear-gradient(
    249deg,
    hsl(var(--base-two)) 0.13%,
    hsl(var(--base)) 55.8%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  width: calc(100% + 20px);
  height: calc(100% + 20px);
}

.achievement-wrapper {
  display: flex;
  align-items: stretch;
  border: 1px solid hsl(var(--white) / 0.1);
  border-radius: 12px;
}

@media screen and (max-width: 424px) {
  .achievement-wrapper {
    flex-direction: column;
  }
}

.achievement-card {
  padding: 23px 20px;
  width: 50%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

@media screen and (max-width: 575px) {
  .achievement-card {
    flex-direction: column;
    padding: 15px;
  }
}

@media screen and (max-width: 424px) {
  .achievement-card {
    width: 100%;
  }

  .achievement-card:not(:last-child) {
    border-bottom: 1px solid hsl(var(--white) / 0.1);
  }
}

.achievement-card:not(:last-child) {
  border-right: 1px solid hsl(var(--white) / 0.1);
}

.achievement-card__icon {
  flex-shrink: 0;
  font-size: 40px;
  display: flex;
  background: var(--base-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.achievement-card__content {
  flex: 1;
}

.achievement-card__title {
  margin-bottom: 12px;
}

@media screen and (max-width: 1399px) {
  .achievement-card__title {
    margin-bottom: 6px;
    font-size: 19px;
  }
}

.achievement-card__count {
  background: var(--base-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 34px;
}

@media screen and (max-width: 1399px) {
  .achievement-card__count {
    font-size: 32px;
  }
}

.card-slider .slick-slide:nth-child(even) .card-item::before {
  top: 5px;
  right: 1px;
  left: auto;
}

.card-slider .slick-slide:nth-child(even) .card-item::after {
  background-position: top center;
}

.card-item {
  position: relative;
  z-index: 1;
  margin: 5px 0;
}

@media screen and (max-width: 767px) {
  .card-item {
    border: 1px solid hsl(var(--white) / 0.1);
    border-radius: 12px;
  }
}

.card-item__content {
  padding: 59px 32px;
  text-align: center;
}

@media screen and (max-width: 1399px) {
  .card-item__content {
    padding: 51px 27px;
  }
}

@media screen and (max-width: 1199px) {
  .card-item__content {
    padding: 91px 27px;
  }
}

@media screen and (max-width: 991px) {
  .card-item__content {
    padding: 67px 27px;
  }
}

@media screen and (max-width: 767px) {
  .card-item__content {
    padding: 30px 15px;
  }
}

.card-item::after,
.card-item::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  height: 100%;
}

@media screen and (max-width: 767px) {
  .card-item::after,
  .card-item::before {
    display: none;
  }
}

.card-item::after {
  -webkit-mask-image: url(../images/thumbs/card-bg.png);
  background-image: url(../images/thumbs/card-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  z-index: -1;
  top: 0;
  left: 0;
  width: 100%;
}

.card-item::before {
  -webkit-mask-image: url(../images/thumbs/cardbg-shape.png);
  z-index: -2;
  top: -5px;
  left: 0;
  background: linear-gradient(
    72deg,
    hsl(var(--base-two)) 7.57%,
    hsl(var(--base)) 103.29%
  );
  width: calc(100% - 10px);
}

.card-item__image {
  width: 75px;
  height: 68px;
  margin: 0px auto 15px;
  position: relative;
  -webkit-mask-image: url(../images/thumbs/cardimage-shape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  background: var(--base-gradient);
  display: grid;
  place-content: center;
}

.card-item__image img {
  width: 32px;
}

.card-item__image-lg {
  width: 95px;
  height: 85px;
}

.card-item__image-lg img {
  width: 100%;
}

.card-item__title {
  margin-bottom: 20px;
  line-height: 1;
}

.card-item__desc {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.winner-section-wrapper {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 50px 24px;
}

@media screen and (max-width: 1199px) {
  .winner-section-wrapper {
    flex-direction: column;
  }
}

.winner-section-wrapper .slick-track {
  display: block !important;
}

.latest-winner {
  width: 320px;
  padding: 35px 25px;
  position: relative;
  min-height: 550px;
  max-height: 550px;
}

@media screen and (max-width: 1199px) {
  .latest-winner {
    width: 100%;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
  }
}

.latest-winner::after,
.latest-winner::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1199px) {
  .latest-winner::after,
  .latest-winner::before {
    display: none;
  }
}

.latest-winner::after {
  -webkit-mask-image: url(../images/shapes/winner-bg.png);
  background-image: url(../images/shapes/winner-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  top: 0;
  left: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
}

.latest-winner::before {
  -webkit-mask-image: url(../images/shapes/winner-shape.png);
  top: 0px;
  right: 0px;
  z-index: -2;
  background: linear-gradient(
    78deg,
    hsl(var(--base-two)) 23.38%,
    hsl(var(--base)) 77.89%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  height: calc(100% + 20px);
  width: calc(100% + 18px);
}

.latest-winner__heading {
  text-align: center;
  margin-bottom: 16px;
  line-height: 1;
}

.latest-winner-item {
  margin-bottom: 17px;
  padding-bottom: 17px;
  border-bottom: 1px solid hsl(var(--white) / 0.1);
}

.latest-winner-item__title {
  margin-bottom: 12px;
  line-height: 1;
}

.latest-winner-item__amount {
  background: var(--base-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.latest-winner-item__amount::selection {
  background: none;
}

.latest-transection {
  flex: 1;
  position: relative;
  padding: 35px 25px 16px 40px;
  top: 20px;
  /* min-height: 550px; */
  background-color: hsl(var(--black));
  border-radius: 12px;
  z-index: 1;
}

@media screen and (max-width: 767px) {
  .latest-transection {
    padding: 20px 15px;
  }
}

@media screen and (max-width: 1399px) {
  .latest-transection {
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.latest-transection::after,
.latest-transection::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1399px) {
  .latest-transection::after,
  .latest-transection::before {
    display: none;
  }
}

/* .latest-transection::after {
  -webkit-mask-image: url(../images/shapes/transection-bg.png);
  background-image: url(../images/shapes/transection-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  top: 0;
  left: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
}

.latest-transection::before {
  -webkit-mask-image: url(../images/shapes/transection-shape.png);
  bottom: 0px;
  left: 0px;
  z-index: -2;
  background: linear-gradient(
    78deg,
    hsl(var(--base-two)) 23.38%,
    hsl(var(--base)) 77.89%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  height: calc(100% + 20px);
  width: calc(100% + 18px);
} */

/* .transection-table-scroller {
  max-height: 500px;
  overflow-y: auto;
} */

.transection-table-scroller::-webkit-scrollbar {
  width: 8px;
}

.transection-table-scroller::-webkit-scrollbar-track {
  background-color: hsl(var(--white) / 0.1);
  border-radius: 10px;
}

.transection-table-scroller::-webkit-scrollbar-thumb {
  background: var(--base-gradient);
  border-radius: 10px;
}

.transection-table thead {
  -webkit-mask-image: url(../images/shapes/table-header-shape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  background: var(--base-gradient);
  background-color: hsl(var(--base-two));
  position: sticky;
  top: 0;
  z-index: 1;
}

.transection-section .transection-table thead {
  -webkit-mask-image: url(../images/shapes/full-table-shape.png) !important;
}

.testimonials-wrapper {
  position: relative;
  padding: 110px 40px 74px;
  z-index: 1;
}

@media screen and (max-width: 1199px) {
  .testimonials-wrapper {
    padding: 80px 40px;
  }
}

@media screen and (max-width: 767px) {
  .testimonials-wrapper {
    padding: 60px 24px;
  }
}

@media screen and (max-width: 1199px) {
  .testimonials-wrapper {
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.testimonials-wrapper::after,
.testimonials-wrapper::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1199px) {
  .testimonials-wrapper::after,
  .testimonials-wrapper::before {
    display: none;
  }
}

.testimonials-wrapper::after {
  -webkit-mask-image: url(../images/thumbs/testimonialOverly.png);
  background-image: url(../images/thumbs/testimonialOverly.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
  z-index: -1;
  height: 100%;
  top: 0;
  width: 100%;
  left: 0;
}

.testimonials-wrapper::before {
  -webkit-mask-image: url(../images/thumbs/testimonialbg.png);
  z-index: -2;
  background: linear-gradient(
    278deg,
    hsl(var(--base-two)) 6.25%,
    hsl(var(--base)) 73.14%
  );
  height: 100%;
  top: 20px;
  width: calc(100% - 1px);
  left: 1px;
}

.winstep-content {
  padding: 89px 60px;
  position: relative;
}

@media screen and (max-width: 1399px) {
  .winstep-content {
    padding: 50px 20px;
  }
}

@media screen and (max-width: 1199px) {
  .winstep-content {
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.winstep-content::after,
.winstep-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

@media screen and (max-width: 1199px) {
  .winstep-content::after,
  .winstep-content::before {
    display: none;
  }
}

.winstep-content::after {
  -webkit-mask-image: url(../images/shapes/win-bg.png);
  background-image: url(../images/shapes/win-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  top: 0;
  left: 0;
  z-index: -1;
}

.winstep-content::before {
  -webkit-mask-image: url(../images/shapes/win-shape.png);
  top: 20px;
  left: 20px;
  z-index: -2;
  background: linear-gradient(
    226deg,
    hsl(var(--base-two)),
    hsl(var(--base)) 97.44%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
}

.winstep-content .slick-initialized .slick-slide {
  display: flex;
  align-items: center;
  justify-content: center;
}

.winstep-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px 50px;
  gap: 50px;
  flex-wrap: wrap;
}

@media screen and (max-width: 1399px) {
  .winstep-wrapper {
    gap: 28px;
  }
}

@media screen and (max-width: 991px) {
  .winstep-wrapper {
    justify-content: center;
    gap: 30px 50px;
  }
}

@media (max-width: 475px) {
  .winstep-wrapper {
    gap: 20px;
  }
}

.winstep-item {
  height: 270px !important;
  width: 270px !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  z-index: 1;
  margin-top: 45px;
}

.winstep-item:last-child::before {
  -webkit-mask-image: url(../images/shapes/bottom-single-shape.png) !important;
  width: calc(100% + 5px);
  -webkit-mask-position: bottom center;
}

@media screen and (max-width: 1399px) {
  .winstep-item:last-child::before {
    width: calc(100% + 20px);
  }
}

@media screen and (max-width: 1399px) {
  .winstep-item {
    height: 270px !important;
    width: 260px !important;
  }
}

@media screen and (max-width: 1199px) {
  .winstep-item {
    height: 190px !important;
    width: 180px !important;
  }
}

@media (max-width: 475px) {
  .winstep-item {
    height: 220px !important;
    width: 210px !important;
  }
}

.winstep-item::after,
.winstep-item::before {
  content: "";
  position: absolute;
  height: 100%;
  z-index: -1;
  border-radius: 50%;
}

.winstep-item::after {
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
  -webkit-mask-image: url(../images/shapes/top-shape.png);
  -webkit-mask-position: top center;
  -webkit-mask-size: auto;
  -webkit-mask-repeat: no-repeat;
  width: calc(100% + 5px);
  top: 0;
}

@media screen and (max-width: 1399px) {
  .winstep-item::after {
    -webkit-mask-size: contain;
    width: calc(100% + 20px);
  }
}

.winstep-item::before {
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
  -webkit-mask-image: url(../images/shapes/bottom-shape.png);
  -webkit-mask-position: bottom right;
  -webkit-mask-size: auto;
  -webkit-mask-repeat: no-repeat;
  width: calc(100% + 182px);
  bottom: 0;
}

@media screen and (max-width: 1399px) {
  .winstep-item::before {
    width: calc(100% + 188px);
  }
}

@media screen and (max-width: 1199px) {
  .winstep-item::before {
    -webkit-mask-image: url(../images/shapes/bottom-single-shape.png) !important;
    width: calc(100% + 18px);
    -webkit-mask-position: bottom center;
    -webkit-mask-size: contain;
  }
}

.winstep-item__content {
  text-align: center;
}

.winstep-item__icon {
  max-width: 64px;
  margin: 0 auto 12px;
}

.winstep-item__title {
  line-height: 1;
}

.winstep-item__badge {
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translateX(-50%);
}

@media screen and (max-width: 1399px) {
  .winstep-item__badge {
    top: -36px;
  }
}

.winstep-item__badge .count {
  height: 85px;
  width: 85px;
  font-size: 42px;
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
  border-radius: 50%;
  font-weight: 700;
  color: hsl(var(--dark));
  display: grid;
  place-content: center;
  position: relative;
}

@media screen and (max-width: 1399px) {
  .winstep-item__badge .count {
    height: 60px;
    width: 60px;
    font-size: 28px;
  }
}

.winstep-item__badge::after {
  content: "";
  position: absolute;
  top: calc(100% - 24px);
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  height: 30px;
  width: 30px;
  background-color: hsl(var(--base));
  z-index: -1;
}

.faq-section-content {
  padding: 90px 60px;
  position: relative;
}

@media screen and (max-width: 1399px) {
  .faq-section-content {
    padding: 50px 30px;
  }
}

@media screen and (max-width: 1199px) {
  .faq-section-content {
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.faq-section-content::after,
.faq-section-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1199px) {
  .faq-section-content::after,
  .faq-section-content::before {
    display: none;
  }
}

.faq-section-content::after {
  -webkit-mask-image: url(../images/shapes/faq-bg.png);
  background-image: url(../images/shapes/faq-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
}

.faq-section-content::before {
  -webkit-mask-image: url(../images/shapes/faq-shape.png);
  height: calc(100% - 10px);
  width: calc(100% + 20px);
  top: 30px;
  right: 1px;
  z-index: -2;
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
}

/* ================================= Blog Section Css Start Here ============================= */
.blog-content {
  padding: 106px 40px 100px;
  position: relative;
}

@media screen and (max-width: 1399px) {
  .blog-content {
    padding: 75px 20px;
  }
}

@media screen and (max-width: 1199px) {
  .blog-content {
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.blog-content::after,
.blog-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1199px) {
  .blog-content::after,
  .blog-content::before {
    display: none;
  }
}

.blog-content::after {
  -webkit-mask-image: url(../images/shapes/blog-bg.png);
  background-image: url(../images/shapes/blog-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.blog-content::before {
  -webkit-mask-image: url(../images/shapes/blog-shape.png);
  height: calc(100% - 60px);
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -2;
  width: calc(100% - 2px);
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
}

.blog-section-content {
  padding: 100px 50px;
  position: relative;
  background-color: hsl(var(--black));
  border: 1px solid hsl(var(--white) / 0.1);
  border-top: 5px solid hsl(var(--base));
  border-radius: 12px;
}

.blog-wrapper {
  display: flex;
  align-items: stretch;
  justify-content: center;
  flex-wrap: wrap;
  gap: 50px 40px;
}

@media screen and (max-width: 1199px) {
  .blog-wrapper {
    gap: 40px 30px;
  }
}

@media screen and (max-width: 424px) {
  .blog-wrapper {
    gap: 30px 20px;
  }
}

.blog-item {
  position: relative;
  width: calc(25% - 30px);
  z-index: 1;
  height: 335px;
}

@media screen and (max-width: 1399px) {
  .blog-item {
    height: 295px;
  }
}

@media screen and (max-width: 1199px) {
  .blog-item {
    width: calc(33.3333333333% - 20px);
    height: 325px;
  }
}

@media screen and (max-width: 991px) {
  .blog-item {
    height: 230px;
  }
}

@media screen and (max-width: 767px) {
  .blog-item {
    width: calc(50% - 15px);
    height: 260px;
  }
}

@media (max-width: 475px) {
  .blog-item {
    width: 100%;
    height: 430px;
  }
}

@media (max-width: 400px) {
  .blog-item {
    width: 100%;
    height: 340px;
  }
}

@media (max-width: 340px) {
  .blog-item {
    width: 100%;
    height: 290px;
  }
}

.blog-item__thumb {
  position: relative;
  z-index: 1;
  height: 100%;
}

.blog-item__thumb img {
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  width: 100%;
}

.blog-item__content {
  position: absolute;
  bottom: 0;
  left: 0px;
  z-index: 2;
  width: 100%;
  padding: 24px;
}

.blog-item__title {
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-item__date {
  color: hsl(var(--dark));
  background: var(--base-gradient);
  display: inline-block;
  padding: 6px 15px;
  font-size: 18px;
  line-height: 1;
  font-weight: 700;
  border-radius: 10px 2px 10px 2px;
  font-family: var(--heading-font);
}

.blog-item:hover .blog-item__readmore {
  opacity: 1;
  visibility: visible;
}

.blog-item:hover .blog-item__readmore .readmore-button {
  transform: scale(1);
}

.blog-item__readmore {
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: hsl(var(--dark) / 0.8);
  top: 0;
  left: 0;
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  display: grid;
  place-content: center;
  transition: all linear 0.3s;
  visibility: hidden;
  opacity: 0;
  z-index: 4;
}

.blog-item__readmore .readmore-button {
  transition: all linear 0.3s;
  transform: scale(0.5);
}

.blog-item::after,
.blog-item::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

.blog-item::after {
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  width: calc(100% + 10px);
  height: calc(100% + 10px);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(
    0deg,
    hsl(var(--base)) -6.03%,
    hsl(var(--base-two)) 113.15%
  );
  z-index: -1;
  border-radius: 10px;
}

.blog-item::before {
  content: "";
  position: absolute;
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
  -webkit-mask-image: url(../images/games/game-bgshape.png);
  background-size: cover;
  background-position: bottom center;
  background-repeat: no-repeat;
  z-index: 2;
  border-radius: 10px;
  background: linear-gradient(
    359deg,
    #0f1923 20.14%,
    rgba(15, 25, 35, 0.65) 35.67%,
    rgba(255, 255, 255, 0) 64.3%
  );
}

.call-section__left {
  flex: 1;
  max-width: 675px;
  width: 100%;
}

.call-section__wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 24px;
}

@media screen and (max-width: 991px) {
  .call-section__wrapper {
    flex-direction: column-reverse;
  }
}

.call-section__thumbs {
  flex-shrink: 0;
}

.call-section .section-heading {
  margin-bottom: 30px;
}

.call-section-content {
  padding: 22px 60px;
  position: relative;
}

@media screen and (max-width: 1399px) {
  .call-section-content {
    padding: 10px 30px;
  }
}

@media screen and (max-width: 1199px) {
  .call-section-content {
    padding: 50px 30px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.call-section-content::after,
.call-section-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1199px) {
  .call-section-content::after,
  .call-section-content::before {
    display: none;
  }
}

.call-section-content::after {
  -webkit-mask-image: url(../images/shapes/action-bg.png);
  background-image: url(../images/shapes/action-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.call-section-content::before {
  -webkit-mask-image: url(../images/shapes/action-shape.png);
  height: 100%;
  bottom: -20px;
  left: 0;
  z-index: -2;
  width: calc(100% + 18px);
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
}

.games-callaction-section .games-callaction {
  max-width: 100%;
}

.about-section {
  margin-top: 100px;
}

@media screen and (max-width: 991px) {
  .about-section {
    margin-top: 80px;
  }
}

@media screen and (max-width: 767px) {
  .about-section {
    margin-top: 60px;
  }
}

@media screen and (max-width: 575px) {
  .about-section {
    margin-top: 40px;
  }
}

.about-section__shape {
  position: absolute;
  left: 50%;
  bottom: calc(100% - 40px);
  transform: translateX(-50%);
}

@media screen and (max-width: 1199px) {
  .about-section__shape {
    bottom: calc(100% - 20px);
  }
}

@media screen and (max-width: 991px) {
  .about-section__shape {
    display: none;
  }
}

@media screen and (max-width: 991px) {
  .about-thumb {
    max-width: 400px;
    margin: 0 auto;
  }
}

.about-section-content {
  position: relative;
  max-width: 1290px;
  width: 100%;
  margin: 0 auto;
  padding: 90px 75px;
}

@media screen and (max-width: 1399px) {
  .about-section-content {
    max-width: 1105px;
    padding: 65px 50px;
  }
}

@media screen and (max-width: 1199px) {
  .about-section-content {
    max-width: 900px;
    padding: 80px 20px;
  }
}

@media screen and (max-width: 991px) {
  .about-section-content {
    padding: 40px 20px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
  }
}

@media screen and (max-width: 767px) {
  .about-section-content {
    padding: 60px 24px;
  }
}

.about-section-content::after,
.about-section-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 991px) {
  .about-section-content::after,
  .about-section-content::before {
    display: none;
  }
}

.about-section-content::after {
  -webkit-mask-image: url(../images/shapes/about-sectionbg.png);
  background-image: url(../images/shapes/about-sectionbg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  z-index: -1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.about-section-content::before {
  top: 47%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-mask-image: url(../images/shapes/about-sectionshape.png);
  z-index: -2;
  background: linear-gradient(
    219deg,
    hsl(var(--base-two)) -6.84%,
    hsl(var(--base)) 71.34%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  width: calc(100% + 80px);
  height: calc(100% + 120px);
}

.about-card-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 25px;
}

@media screen and (max-width: 1199px) {
  .about-card-wrapper {
    gap: 16px;
  }
}

.about-card {
  height: 67px;
  width: 250px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  padding: 0 15px;
  position: relative;
  z-index: 1;
}

@media screen and (max-width: 1399px) {
  .about-card {
    height: 58px;
    width: 220px;
  }
}

@media screen and (max-width: 991px) {
  .about-card {
    height: 85px;
    width: 319px;
  }
}

@media screen and (max-width: 767px) {
  .about-card {
    height: 60px;
    width: 225px;
  }
}

@media screen and (max-width: 575px) {
  .about-card {
    padding: 20px 10px;
    border-radius: 12px;
    border: 1px solid hsl(var(--white) / 0.1);
    width: calc(50% - 8px);
  }
}

@media screen and (max-width: 424px) {
  .about-card {
    width: 100%;
    padding: 30px 20px;
  }
}

.about-card::after,
.about-card::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 575px) {
  .about-card::after,
  .about-card::before {
    display: none;
  }
}

.about-card::after {
  -webkit-mask-image: url(../images/shapes/acard-bg.png);
  background-image: url(../images/shapes/acard-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  z-index: -1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.about-card::before {
  top: 8px;
  left: 3px;
  -webkit-mask-image: url(../images/shapes/acard-shape.png);
  z-index: -2;
  background: linear-gradient(
    219deg,
    hsl(var(--base-two)) -6.84%,
    hsl(var(--base)) 71.34%
  );
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  width: 100%;
  height: calc(100% - 5px);
}

.about-card__icon {
  background: var(--base-gradient);
  display: flex;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 30px;
}

@media screen and (max-width: 991px) {
  .about-card__title {
    font-size: 24px;
  }
}

@media screen and (max-width: 767px) {
  .about-card__title {
    font-size: 20px;
  }
}

.info-wrapper {
  display: flex;
  align-items: stretch;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px 24px;
}

@media screen and (max-width: 1199px) {
  .info-wrapper {
    gap: 24px;
  }
}

.info-card {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  width: 430px;
  height: 158px;
  padding: 36px 40px;
  position: relative;
}

@media screen and (max-width: 1199px) {
  .info-card {
    width: calc(50% - 12px);
    height: 158px;
    padding: 20px 24px;
  }
}

@media screen and (max-width: 991px) {
  .info-card {
    width: 336px;
    height: 122px;
    padding: 20px 24px;
  }
}

@media screen and (max-width: 767px) {
  .info-card {
    width: calc(50% - 12px);
    padding: 20px 24px;
    height: auto;
    flex-direction: column;
    text-align: center;
    border-radius: 12px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
  }
}

@media screen and (max-width: 575px) {
  .info-card {
    width: 100%;
  }
}

.info-card::after,
.info-card::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 767px) {
  .info-card::after,
  .info-card::before {
    display: none;
  }
}

.info-card::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/info-cardbg.png);
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
  background-color: hsl(var(--black));
}

.info-card::before {
  height: calc(100% + 6px);
  width: calc(100% + 10px);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-mask-image: url(../images/shapes/info-cardbg.png);
  z-index: -2;
  background: linear-gradient(
    259deg,
    hsl(var(--base)) 1.66%,
    hsl(var(--base-two)) 109.25%
  );
}

.info-card__icon {
  flex-shrink: 0;
  height: 65px;
  width: 65px;
  background-color: hsl(var(--black));
  display: grid;
  place-content: center;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.info-card__icon::before,
.info-card__icon::after {
  content: "";
  position: absolute;
}

.info-card__icon::before {
  background-color: hsl(var(--black));
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  border-radius: 50%;
}

.info-card__icon::after {
  height: calc(100% + 4px);
  width: calc(100% + 4px);
  top: 50%;
  left: 50%;
  transform: translate(-51%, -49%);
  background: var(--base-gradient);
  border-radius: 50%;
  z-index: -2;
}

.info-card__icon .icon {
  font-size: 30px;
  background: var(--base-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.info-card__content {
  flex: 1;
}

.info-card__title {
  margin-bottom: 12px;
}

.info-card__list-link {
  color: var(--white) / 0.6;
}

.info-card__list-link:hover {
  color: hsl(var(--base));
}

.contact-wrapper {
  max-width: 1293px;
  margin: 0px auto;
  position: relative;
  padding: 65px 80px 100px;
  min-height: 950px;
  max-height: 980px;
}

@media screen and (max-width: 1399px) {
  .contact-wrapper {
    padding: 40px 30px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    min-height: auto;
    max-height: auto;
  }
}

@media screen and (max-width: 575px) {
  .contact-wrapper {
    padding: 30px 15px;
  }
}

.contact-wrapper::after,
.contact-wrapper::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1399px) {
  .contact-wrapper::after,
  .contact-wrapper::before {
    display: none;
  }
}

.contact-wrapper::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/contact-shape.png);
  background-image: url(../images/shapes/contact-shape.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.contact-wrapper::before {
  height: calc(100% + 28px);
  width: calc(100% + 42px);
  top: -15px;
  left: -20px;
  -webkit-mask-image: url(../images/shapes/contact-bg.png);
  z-index: -2;
  background: linear-gradient(
    259deg,
    hsl(var(--base)) 1.66%,
    hsl(var(--base-two)) 109.25%
  );
}

@media screen and (max-width: 991px) {
  .contact-thumb {
    max-width: 440px;
    margin: 0 auto;
  }
}

.contact-form {
  padding-right: 60px;
  border: 1px solid var(--vase);
  border: 0;
  border-right-width: 1px;
  border-style: solid;
  border-image: var(--base-gradient);
  border-image-slice: 1;
}

@media screen and (max-width: 991px) {
  .contact-form {
    border: 0;
    padding: 0;
  }
}

.contact-form__title {
  margin-bottom: 12px;
  text-align: center;
}

.withdraw-content {
  position: relative;
  max-width: 550px;
  width: 100%;
  padding: 90px 50px 105px;
  margin: 0 auto;
}

@media screen and (max-width: 1399px) {
  .withdraw-content {
    max-width: 500px;
    padding: 60px 50px 90px;
  }
}

@media screen and (max-width: 575px) {
  .withdraw-content {
    border-radius: 12px;
    background-color: hsl(var(--black));
    border-top: 5px solid hsl(var(--base));
    border: 1px solid hsl(var(--white) / 0.1);
    padding: 40px 30px;
  }
}

@media screen and (max-width: 424px) {
  .withdraw-content {
    padding: 40px 15px 20px;
  }
}

.withdraw-content::after,
.withdraw-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 575px) {
  .withdraw-content::after,
  .withdraw-content::before {
    display: none;
  }
}

.withdraw-content::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/widraw-bg.png);
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.withdraw-content::before {
  height: calc(100% + 30px);
  width: calc(100% + 20px);
  top: -15px;
  left: -10px;
  -webkit-mask-image: url(../images/shapes/widraw-shape.png);
  z-index: -2;
  background: var(--base-gradient);
}

.withdraw-content__title {
  text-align: center;
  margin-bottom: 32px;
  font-size: 32px;
}

@media screen and (max-width: 991px) {
  .withdraw-content__title {
    font-size: 28px;
  }
}

@media screen and (max-width: 767px) {
  .withdraw-content__title {
    font-size: 28px;
  }
}

.referrals {
  padding-top: 150px;
  padding-bottom: 100px;
}

@media screen and (max-width: 1199px) {
  .referrals {
    padding-top: 100px;
    padding-bottom: 70px;
  }
}

@media screen and (max-width: 767px) {
  .referrals {
    padding-top: 80px;
    padding-bottom: 50px;
  }
}

@media screen and (max-width: 575px) {
  .referrals {
    padding-top: 60px;
    padding-bottom: 40px;
  }
}

.referrals-content {
  position: relative;
  max-width: 1134px;
  width: 100%;
  padding: 80px 65px 35px;
  margin: 0 auto;
}

@media screen and (max-width: 1199px) {
  .referrals-content {
    padding: 65px 50px 30px;
  }
}

@media screen and (max-width: 991px) {
  .referrals-content {
    padding: 35px 40px 12px;
  }
}

.referrals-content__shape,
.referrals-content__shapetwo {
  position: absolute;
  z-index: -3;
}

@media screen and (max-width: 767px) {
  .referrals-content__shape,
  .referrals-content__shapetwo {
    display: none;
  }
}

.referrals-content__shape {
  bottom: calc(100% + 10px);
  left: 100px;
}

.referrals-content__shapetwo {
  top: calc(100% + 10px);
  right: 100px;
}

@media (min-width: 1200px) {
  .referrals-content .form--control,
  .referrals-content .input-inner__text {
    height: 70px;
  }

  .referrals-content .input-inner__text {
    font-size: 30px;
  }
}

@media screen and (max-width: 767px) {
  .referrals-content {
    border-radius: 12px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    padding: 30px 20px;
  }
}

.referrals-content::after,
.referrals-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 767px) {
  .referrals-content::after,
  .referrals-content::before {
    display: none;
  }
}

.referrals-content::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/referral-bg.png);
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.referrals-content::before {
  height: calc(100% + 30px);
  width: calc(100% + 20px);
  top: -15px;
  left: -10px;
  -webkit-mask-image: url(../images/shapes/referral-shape.png);
  z-index: -2;
  background: var(--base-gradient);
}

.ticketform-inner {
  max-width: 1090px;
  margin: 0px auto;
  position: relative;
  padding: 90px 60px 140px 80px;
}

@media (min-width: 1200px) {
  .ticketform-inner__scroller {
    max-height: 665px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media screen and (max-width: 1199px) {
  .ticketform-inner {
    padding: 40px 30px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
  }
}

@media screen and (max-width: 575px) {
  .ticketform-inner {
    padding: 30px 15px;
  }
}

.ticketform-inner::after,
.ticketform-inner::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1199px) {
  .ticketform-inner::after,
  .ticketform-inner::before {
    display: none;
  }
}

.ticketform-inner::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/registration-bg.png);
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.ticketform-inner::before {
  height: calc(100% + 35px);
  width: calc(100% + 34px);
  top: -17px;
  left: -16px;
  -webkit-mask-image: url(../images/shapes/registration-shape.png);
  z-index: -2;
  background: linear-gradient(
    259deg,
    hsl(var(--base)) 1.66%,
    hsl(var(--base-two)) 109.25%
  );
}

.ticketform-inner__ticket {
  text-align: center;
  margin-bottom: 32px;
  font-size: 40px;
}

@media screen and (max-width: 991px) {
  .ticketform-inner__ticket {
    font-size: 32px;
  }
}

@media screen and (max-width: 767px) {
  .ticketform-inner__ticket {
    font-size: 28px;
  }
}

.ticketform-inner .plus-icon,
.ticketform-inner .remove-button {
  width: 56px;
  height: 56px;
  flex-shrink: 0;
  border-radius: 4px;
  background: var(--base-gradient);
  display: grid;
  place-content: center;
  font-size: 24px;
  color: hsl(var(--black));
  cursor: pointer;
}

@media screen and (max-width: 991px) {
  .ticketform-inner .plus-icon,
  .ticketform-inner .remove-button {
    height: 46px;
    width: 46px;
    font-size: 20px;
  }
}

@media screen and (max-width: 575px) {
  .ticketform-inner .plus-icon,
  .ticketform-inner .remove-button {
    height: 40px;
    width: 40px;
    font-size: 18px;
  }
}

.form--control[type="file"] {
  line-height: 56px;
}

@media screen and (max-width: 424px) {
  .form--control[type="file"] {
    line-height: 46px;
  }
}

.form--control[type="file"]::-webkit-file-upload-button {
  background: var(--base-gradient);
  border-radius: 4px;
  padding: 0px 24px;
  margin: 0;
  margin-right: 20px;
  color: hsl(var(--black)) !important;
  cursor: pointer;
  font-size: 18px;
  font-weight: 500;
  line-height: 54px;
  border: 0;
}

@media screen and (max-width: 424px) {
  .form--control[type="file"]::-webkit-file-upload-button {
    line-height: 44px;
    font-size: 15px;
    padding: 0px 12px;
  }
}

.form--control[type="file"]::-webkit-file-upload-button:hover {
  border: 0;
}

.filter_wrapper {
  position: relative;
  width: 100%;
  padding: 40px 50px 35px;
  margin: 60px 0;
}

@media screen and (max-width: 1399px) {
  .filter_wrapper {
    padding: 25px 50px 24px;
  }
}

@media screen and (max-width: 1199px) {
  .filter_wrapper {
    margin: 0 0;
  }
}

@media screen and (max-width: 991px) {
  .filter_wrapper {
    padding: 35px 40px 12px;
  }
}

.filter_wrapper__shape,
.filter_wrapper__shapetwo {
  position: absolute;
  z-index: -3;
}

.filter_wrapper__shape img,
.filter_wrapper__shapetwo img {
  height: 40px;
  width: 220px;
}

@media screen and (max-width: 1199px) {
  .filter_wrapper__shape,
  .filter_wrapper__shapetwo {
    display: none;
  }
}

.filter_wrapper__shape {
  bottom: calc(100% + 10px);
  left: 100px;
}

.filter_wrapper__shapetwo {
  top: calc(100% + 7px);
  right: 100px;
}

@media screen and (max-width: 1199px) {
  .filter_wrapper {
    border-radius: 12px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    padding: 30px 20px;
  }
}

.filter_wrapper::after,
.filter_wrapper::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1199px) {
  .filter_wrapper::after,
  .filter_wrapper::before {
    display: none;
  }
}

.filter_wrapper::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/filter-bg.png);
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.filter_wrapper::before {
  height: calc(100% + 22px);
  width: calc(100% + 20px);
  top: -12px;
  left: -10px;
  -webkit-mask-image: url(../images/shapes/filter-shape.png);
  z-index: -2;
  background: var(--base-gradient);
}

.filter-input-wrapper {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 12px 24px;
  flex-wrap: wrap;
}

.filter-input-wrapper .require {
  color: hsl(var(--danger));
}

@media screen and (max-width: 575px) {
  .filter-input-wrapper {
    gap: 16px;
  }
}

.filter-input-inner {
  width: calc(33.3333333333% - 66.6666666667px - 24px);
}

@media screen and (max-width: 1199px) {
  .filter-input-inner {
    width: calc(50% - 12px);
  }
}

@media screen and (max-width: 575px) {
  .filter-input-inner {
    width: 100%;
  }
}

.filter-input-button {
  width: 200px;
}

.filter-input-button .btn--gradient {
  height: 54px;
}

@media screen and (max-width: 1199px) {
  .filter-input-button {
    width: calc(50% - 12px);
  }
}

@media screen and (max-width: 575px) {
  .filter-input-button {
    width: 100%;
  }
}

.headtail-body {
  position: relative;
}

.headtail-body__flip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.headtail-body .coin-flipbox {
  /* height: 240px; */
  /* width: 240px; */
  position: relative;
}

@media screen and (max-width: 1399px) {
  .headtail-body .coin-flipbox {
    height: 200px;
    width: 200px;
  }
}

@media screen and (max-width: 991px) {
  .headtail-body .coin-flipbox {
    height: 240px;
    width: 240px;
  }
}

@media screen and (max-width: 767px) {
  .headtail-body .coin-flipbox {
    height: 190px;
    width: 190px;
  }
}

@media screen and (max-width: 575px) {
  .headtail-body .coin-flipbox {
    height: 150px;
    width: 150px;
  }
}

@media screen and (max-width: 424px) {
  .headtail-body .coin-flipbox {
    height: 120px;
    width: 120px;
  }
}

.headtail-body .coin-flipbox__front,
.headtail-body .coin-flipbox__back {
  position: absolute;
}

.headtail-body .coin-flipbox__front img,
.headtail-body .coin-flipbox__back img {
  height: 100%;
  width: 100%;
}

.headtail-wrapper {
  position: relative;
  max-width: 605px;
  padding: 60px 40px 45px;
  margin-left: auto;
}

@media screen and (max-width: 1399px) {
  .headtail-wrapper {
    padding: 40px 40px 35px;
  }
}

@media screen and (max-width: 1199px) {
  .headtail-wrapper {
    padding: 30px 20px;
  }
}

@media screen and (max-width: 991px) {
  .headtail-wrapper {
    padding: 80px 40px;
    max-width: 100%;
  }
}

@media screen and (max-width: 767px) {
  .headtail-wrapper {
    padding: 30px 20px;
  }
}

@media screen and (max-width: 575px) {
  .headtail-wrapper {
    padding: 50px 30px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.headtail-wrapper::after,
.headtail-wrapper::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 575px) {
  .headtail-wrapper::after,
  .headtail-wrapper::before {
    display: none;
  }
}

.headtail-wrapper::after {
  -webkit-mask-image: url(../images/shapes/headtail-bg.png);
  background-image: url(../images/shapes/headtail-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.headtail-wrapper::before {
  -webkit-mask-image: url(../images/shapes/headtail-shape.png);
  bottom: -20px;
  left: -18px;
  z-index: -2;
  height: calc(100% + 18px);
  width: calc(100% + 15px);
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
}

.game-contet-title {
  text-align: center;
  margin-bottom: 24px;
  font-size: 32px;
}

.game-contet-title .text {
  background: var(--base-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.game-contet-title .text::selection {
  -webkit-background-clip: unset;
  -webkit-text-fill-color: hsl(var(--white));
}

.game-playbtn {
  max-width: 375px;
  margin: 0 auto;
}

@media screen and (max-width: 1199px) {
  .input-inner-note {
    font-size: 12px;
  }
}

.headtail-slect {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  margin-block: 40px;
  cursor: pointer;
}

@media screen and (max-width: 1199px) {
  .headtail-slect {
    gap: 16px;
    margin-block: 20px;
  }
}

@media screen and (max-width: 991px) {
  .headtail-slect {
    margin-block: 50px;
  }
}

@media screen and (max-width: 767px) {
  .headtail-slect {
    margin-block: 30px;
  }
}

.headtail-slect__box {
  width: 175px;
  height: 165px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid hsl(var(--white) / 0.1);
  background: hsl(var(--dark));
}

@media screen and (max-width: 1399px) {
  .headtail-slect__box {
    width: 160px;
    height: 150px;
  }
}

@media screen and (max-width: 1199px) {
  .headtail-slect__box {
    width: 140px;
    height: 130px;
  }
}

@media screen and (max-width: 991px) {
  .headtail-slect__box {
    width: 195px;
    height: 185px;
  }
}

@media screen and (max-width: 767px) {
  .headtail-slect__box {
    width: 150px;
    height: 140px;
  }
}

@media screen and (max-width: 424px) {
  .headtail-slect__box {
    width: 108px;
    height: 100px;
  }
}

.headtail-slect__image img {
  width: 120px;
}

@media screen and (max-width: 1399px) {
  .headtail-slect__image img {
    width: 100px;
  }
}

@media screen and (max-width: 1199px) {
  .headtail-slect__image img {
    width: 90px;
  }
}

@media screen and (max-width: 991px) {
  .headtail-slect__image img {
    width: 125px;
  }
}

@media screen and (max-width: 767px) {
  .headtail-slect__image img {
    width: 100px;
  }
}

@media screen and (max-width: 424px) {
  .headtail-slect__image img {
    width: 70px;
  }
}

.game-select-box {
  border: 1px solid transparent;
  position: relative;
}

.game-select-box::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: hsl(var(--black) / 0.5);
  overflow: hidden;
  border-radius: inherit;
  visibility: hidden;
  opacity: 0;
  top: 0;
  left: 0;
}

.game-select-box.active {
  border: 1px solid hsl(var(--base-two));
}

.game-select-box.active::after {
  visibility: visible;
  opacity: 1;
}

.card-box-image img {
  width: 90px;
}

@media screen and (max-width: 1399px) {
  .card-box-image img {
    width: 80px;
  }
}

@media screen and (max-width: 1199px) {
  .card-box-image img {
    width: 70px;
  }
}

@media screen and (max-width: 991px) {
  .card-box-image img {
    width: 100px;
  }
}

@media screen and (max-width: 767px) {
  .card-box-image img {
    width: 80px;
  }
}

@media screen and (max-width: 575px) {
  .card-box-image img {
    width: 70px;
  }
}

@media screen and (max-width: 424px) {
  .card-box-image img {
    width: 50px;
  }
}

.diceroll-slect {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 75px;
  margin-bottom: 105px;
}

@media (min-width: 1200px) and (max-width: 1400px) {
  .diceroll-slect {
    margin-top: 75px;
    margin-bottom: 90px;
  }
}

@media (min-width: 992px) and (max-width: 1200px) {
  .diceroll-slect {
    margin-top: 35px;
    margin-bottom: 43px;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .diceroll-slect {
    margin-top: 60px;
    margin-bottom: 80px;
  }
}

@media screen and (max-width: 575px) {
  .diceroll-slect {
    margin: 40px 0;
  }
}

.diceroll-selct {
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;
}

.diceroll-selct img {
  border-radius: 10px;
}

@media (min-width: 992px) and (max-width: 1200px) {
  .diceroll-selct img {
    width: 50px;
  }
}

@media screen and (max-width: 575px) {
  .diceroll-selct img {
    width: 50px;
  }
}

.diceroll-selct.active {
  border-width: 2px;
}

.rockselect-box {
  width: 145px;
  height: 145px;
  border-radius: 12px;
  display: grid;
  place-content: center;
  border: 1px solid hsl(var(--white) / 0.1);
  background: hsl(var(--dark));
}

@media (min-width: 992px) and (max-width: 1200px) {
  .rockselect-box {
    width: 120px;
    height: 120px;
  }
}

@media screen and (max-width: 575px) {
  .rockselect-box {
    width: 100px;
    height: 100px;
  }
}

.poolselect-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-block: 27px;
  cursor: pointer;
}

@media screen and (max-width: 1199px) {
  .poolselect-wrapper {
    gap: 8px;
    margin-block: 20px;
  }
}

@media screen and (max-width: 991px) {
  .poolselect-wrapper {
    margin-block: 50px;
  }
}

@media screen and (max-width: 767px) {
  .poolselect-wrapper {
    margin-block: 30px;
  }
}

.poolselect-box {
  width: 95px;
  height: 85px;
  border-radius: 12px;
  display: grid;
  place-content: center;
  border: 1px solid hsl(var(--white) / 0.1);
  background: hsl(var(--dark));
}

@media (min-width: 992px) and (max-width: 1200px) {
  .poolselect-box {
    width: 80px;
    height: 80px;
  }

  .poolselect-box__image img {
    width: 40px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .poolselect-box {
    width: 120px;
    height: 100px;
  }

  .poolselect-box__image img {
    width: 60px;
  }
}

@media screen and (max-width: 424px) {
  .poolselect-box {
    width: 72px;
    height: 72px;
  }

  .poolselect-box__image img {
    width: 40px;
  }
}

.game-contet__sm::after {
  -webkit-mask-image: url(../images/shapes/gamesm-ng.png);
  background-image: url(../images/shapes/gamesm-ng.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.game-contet__sm::before {
  -webkit-mask-image: url(../images/shapes/gamesm-shape.png);
  bottom: -20px;
  left: -18px;
  z-index: -2;
  height: calc(100% + 18px);
  width: calc(100% + 15px);
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
}

.game-contet__sm .game-playbtn {
  margin-top: 98px;
}

@media screen and (max-width: 1399px) {
  .game-contet__sm .game-playbtn {
    margin-top: 94px;
  }
}

@media screen and (max-width: 1199px) {
  .game-contet__sm .game-playbtn {
    margin-top: 24px;
  }
}

@media screen and (max-width: 991px) {
  .game-contet__sm .game-playbtn {
    margin-top: 115px;
  }
}

@media screen and (max-width: 767px) {
  .game-contet__sm .game-playbtn {
    margin-top: 80px;
  }
}

@media screen and (max-width: 575px) {
  .game-contet__sm .game-playbtn {
    margin-top: 40px;
  }
}

.keno-history-content {
  position: relative;
  max-width: 605px;
  padding: 55px 40px 45px;
  margin-left: auto;
}

.keno-history-content__desc {
  margin-bottom: 30px;
  text-align: center;
}

@media screen and (max-width: 1399px) {
  .keno-history-content {
    padding: 40px 40px 35px;
  }
}

@media screen and (max-width: 1199px) {
  .keno-history-content {
    padding: 30px 20px;
  }
}

@media screen and (max-width: 991px) {
  .keno-history-content {
    padding: 80px 40px;
    max-width: 100%;
  }
}

@media screen and (max-width: 767px) {
  .keno-history-content {
    padding: 30px 20px;
  }
}

@media screen and (max-width: 575px) {
  .keno-history-content {
    padding: 50px 30px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
    top: 0;
  }
}

.keno-history-content::after,
.keno-history-content::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 575px) {
  .keno-history-content::after,
  .keno-history-content::before {
    display: none;
  }
}

.keno-history-content::after {
  -webkit-mask-image: url(../images/shapes/keno-bg.png);
  background-image: url(../images/shapes/keno-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: bottom center;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.keno-history-content::before {
  -webkit-mask-image: url(../images/shapes/keno-shape.png);
  bottom: -20px;
  left: -18px;
  z-index: -2;
  height: calc(100% + 18px);
  width: calc(100% + 15px);
  background: linear-gradient(
    180deg,
    hsl(var(--base-two)) 0%,
    hsl(var(--base)) 100%
  );
}

.keno-history {
  max-height: 342px;
  min-height: 342px;
  overflow-y: auto;
  padding-right: 10px;
}

.keno-history__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.keno-history__item:not(:last-child) {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid hsl(var(--white) / 0.1);
}

.keno-history__text {
  color: hsl(var(--white));
}

.keno-history__count {
  background: var(--base-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 500;
}

/* =========================================== Blog Details Css Start ==============================*/
.blog-details {
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  box-shadow: 0px 2px 15px hsl(var(--white) / 0.25);
}

.blog-details__thumb {
  height: 450px;
  max-height: 100%;
  padding: 20px;
}

.blog-details__thumb img {
  border-radius: 6px;
}

@media screen and (max-width: 991px) {
  .blog-details__thumb {
    height: 350px;
  }
}

@media screen and (max-width: 767px) {
  .blog-details__thumb {
    height: 300px;
  }
}

.blog-details__content {
  padding: 35px 25px;
}

@media screen and (max-width: 767px) {
  .blog-details__content {
    padding: 30px 20px;
  }
}

@media screen and (max-width: 575px) {
  .blog-details__content {
    padding: 25px 15px;
  }
}

.blog-details__title {
  margin-top: 20px;
  margin-bottom: 15px;
}

.blog-details__desc {
  margin-bottom: 15px;
}

.blog-details .social-list__link {
  border: 1px solid hsl(var(--black) / 0.1);
  color: hsl(var(--body-color));
}

.blog-details .social-list__link:hover {
  border-color: hsl(var(--base));
  color: hsl(var(--white));
}

.quote-text {
  background-color: hsl(var(--base) / 0.1);
  padding: 30px 20px;
  border-radius: 5px;
  border-left: 3px solid hsl(var(--base));
  margin-bottom: 20px;
}

@media screen and (max-width: 767px) {
  .quote-text {
    padding: 25px 15px;
  }
}
/* =========================================== Account Css Start =========================*/

.account-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 715px;
  width: 100%;
  padding: 75px 50px 95px;
  margin: 0 auto;
  min-height: 950px;
  max-height: 950px;
}

.account-inner .account-form {
  width: 100%;
}

@media screen and (max-width: 1199px) {
  .account-inner {
    padding: 95px 40px;
  }
}

@media screen and (max-width: 991px) {
  .account-inner {
    min-height: 925px;
    max-height: 925px;
  }
}

@media screen and (max-width: 767px) {
  .account-inner {
    border-radius: 12px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    padding: 40px 30px;
    max-height: auto;
    min-height: auto;
  }
}

@media screen and (max-width: 424px) {
  .account-inner {
    padding: 40px 15px 20px;
  }
}

.account-inner::after,
.account-inner::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 767px) {
  .account-inner::after,
  .account-inner::before {
    display: none;
  }
}

.account-inner::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/login-bg.png);
  background-image: url(../images/shapes/login-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.account-inner::before {
  height: calc(100% + 30px);
  width: calc(100% + 20px);
  top: -15px;
  left: -10px;
  -webkit-mask-image: url(../images/shapes/login-shape.png);
  z-index: -2;
  background: var(--base-gradient);
}

.account-form__content {
  margin-bottom: 24px;
}

.account-form__logo {
  max-width: 190px;
  margin: 0 auto 10px;
  display: block;
}

.account-form__desc {
  font-size: 18px;
  text-align: center;
}

.accout-bg {
  min-height: 100vh;
  z-index: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url(../images/thumbs/bgfit-image.png);
}

.accout-bg::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -3;
  background: linear-gradient(
    2deg,
    hsl(var(--dark)) 12.21%,
    hsl(var(--dark) / 0.6) 86.4%
  );
}

.account-note {
  color: hsl(var(--white));
}

.account-note a {
  background: var(--base-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  display: inline-block;
}

.back-button {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: hsl(var(--white) / 0.2);
  border-radius: 50%;
  display: grid;
  place-content: center;
}

@media screen and (max-width: 575px) {
  .back-button {
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
  }
}

@media screen and (max-width: 424px) {
  .back-button {
    top: 10px;
    right: 10px;
  }
}

.back-button .icon {
  background: var(--base-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
}

@media screen and (max-width: 575px) {
  .back-button .icon {
    font-size: 18px;
  }
}

.forgot-password {
  font-weight: 600;
  color: hsl(var(--white));
}

.forgot-password:hover {
  color: hsl(var(--base));
}

/* ============================================ Account Css End ====================================*/

.registration-inner {
  margin: 0px auto;
  position: relative;
  padding: 50px 80px 80px;
  display: flex;
  align-items: center;
}

@media (min-width: 1400px) {
  .registration-inner {
    min-height: 1100px;
  }
}

@media screen and (max-width: 1399px) {
  .registration-inner {
    padding: 40px 30px;
    background-color: hsl(var(--black));
    border: 1px solid hsl(var(--white) / 0.1);
    border-top: 5px solid hsl(var(--base));
    border-radius: 12px;
  }
}

@media screen and (max-width: 575px) {
  .registration-inner {
    padding: 30px 15px;
  }
}

.registration-inner::after,
.registration-inner::before {
  content: "";
  position: absolute;
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
}

@media screen and (max-width: 1399px) {
  .registration-inner::after,
  .registration-inner::before {
    display: none;
  }
}

.registration-inner::after {
  height: 100%;
  width: 100%;
  -webkit-mask-image: url(../images/shapes/registration-bg.png);
  background-image: url(../images/shapes/registration-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-color: hsl(var(--black));
  bottom: 0;
  right: 0;
  z-index: -1;
}

.registration-inner::before {
  height: calc(100% + 35px);
  width: calc(100% + 34px);
  top: -17px;
  left: -16px;
  -webkit-mask-image: url(../images/shapes/registration-shape.png);
  z-index: -2;
  background: linear-gradient(
    259deg,
    hsl(var(--base)) 1.66%,
    hsl(var(--base-two)) 109.25%
  );
}
/*# sourceMappingURL=main.css.map */
.input-group-text {
  background-color: hsl(var(--base));
  border: none;
  color: hsl(var(--white));
}

.menu_has_children .sub-menu {
  position: absolute;
  width: 220px;
  top: 105%;
  left: -20px;
  background-color: #0f1a24;
  padding: 10px 0;
  box-shadow: 0px 18px 54px -8px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  transition: all 0.3s;
  opacity: 0;
  visibility: hidden;
  border-top: 3px solid hsl(var(--base));
  z-index: 9;
}

.menu_has_children .nav-link {
  position: relative;
}

.menu_has_children .sub-menu li a {
  color: hsl(var(--white));
  padding: 7px 15px;
  font-family: var(--heading-font);
  font-size: 24px;
  display: block;
  line-height: 1;
  transition: all linear 0.3s;
}

.menu_has_children .sub-menu li a:hover {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
}

.menu_has_children:hover .sub-menu {
  top: 100%;
  opacity: 1;
  visibility: visible;
}

.menu_has_children > a::after {
  content: "\f107";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: #ffffff;
  transition: all 0.3s;
  display: none;
}

@media (max-width: 1199px) {
  .menu_has_children > a::after {
    display: block;
  }

  .menu_has_children .sub-menu {
    opacity: 1;
    visibility: visible;
    display: none;
    position: static;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
    width: 100%;
    border-radius: 0;
    background-color: hsl(var(--black) / 0.3);
  }

  .open.menu_has_children .sub-menu {
    display: block;
  }
}

.mobile-code {
  font-size: 22px;
  font-family: var(--heading-font);
  font-weight: 700;
}

.list-group-item {
  background-color: transparent;
  color: hsl(var(--white) / 0.6);
  border: 1px solid hsl(var(--base) / 0.2);
}

.support-card {
  border: 1px solid hsl(var(--base) / 0.5);
  border-radius: 10px;
  background: hsl(var(--dark));
}

.support-card__title {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

@media screen and (min-width: 768px) {
  .support-card__title {
    margin-bottom: 0;
  }
}

.support-card__head {
  padding: 12px 15px;
  border-bottom: 1px solid hsl(var(--base) / 0.5);
}

@media screen and (min-width: 768px) {
  .support-card__head {
    display: flex;
    justify-content: space-between;
  }
}

.support-card__date {
  display: block;
  line-height: 1;
}

.support-card__body {
  padding: 12px 15px;
}

.support-card__body-text {
  font-size: 14px;
  margin-bottom: 0;
  color: hsl(var(--white) / 0.8);
}

.support-card__list {
  --gap: 0.5rem;
}

@media screen and (min-width: 768px) {
  .support-card__list {
    margin-top: 1rem;
  }
}

.support-card__file {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  line-height: 1;
  color: hsl(var(--base));
}

.win-loss-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100vh;
  background-color: transparent;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transform: scale(0.5);
  transition: all 0.5s ease-in-out;
  opacity: 0;
  visibility: hidden;
}

.win-loss-popup.active {
  transform: scale(1);
  opacity: 1;
  visibility: visible;
}

.win-loss-popup__inner {
  width: 480px;
  max-width: 100%;
  text-align: center;
  background-color: #01162f;
  border-radius: 8px;
  border: 3px solid rgba(255, 255, 255, 0.15);
}

@media (max-width: 575px) {
  .win-loss-popup__inner {
    width: 300px;
  }
}

.win-loss-popup__header {
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.win-loss-popup__body {
  padding: 20px 30px;
}

.win-loss-popup__body .icon {
  font-size: 200px;
  line-height: 1;
  color: #ed1569;
  text-shadow: 0 5px 15px #ed1569;
}

.win-loss-popup__footer {
  padding: 20px 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

/* mine games */

.mine-box-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 16px;
}

.mine-box {
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  z-index: 2;
  height: 80px;
  width: calc(100% / 3 - 13px);
}

@media (min-width: 425px) {
  .mine-box {
    width: calc(100% / 4 - 12px);
  }
}

@media (min-width: 768px) {
  .mine-box {
    width: calc(100% / 5 - 13px);
  }
}

@media (min-width: 992px) {
  .mine-box {
    width: calc(100% / 4 - 12px);
  }
}

@media (min-width: 1200px) {
  .mine-box {
    width: calc(100% / 5 - 13px);
  }
}

.mine-box-wrapper {
  height: 100%;
  transform-origin: center right;
  transform-style: preserve-3d;
  transition: all 0.4s cubic-bezier(0.57, 0.5, 0.15, 0.93);
  width: 100%;
}

.mine-box.active-hidden .mine-box-wrapper {
  transform: translateX(-100%) rotateY(-180deg);
}

.mine-box-front {
  align-items: center;
  backface-visibility: hidden;
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  padding: 8px;
  position: absolute;
  width: 100%;
  background-color: #232640;
}

.mine-box-front img {
  height: 95%;
  pointer-events: none;
  transition: all 0.3s ease;
  user-select: none;
  width: 95%;
  object-fit: contain;
}

.mine-box.active-hidden {
  cursor: not-allowed;
  pointer-events: none;
}

.mine-box.gold-box {
  pointer-events: none;
}

.mine-box-hidden {
  align-items: center;
  backface-visibility: hidden;
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  padding: 8px;
  position: absolute;
  width: 100%;
  transform: rotateY(180deg);
  background: #232640;
}

.mine-box.active-hidden .mine-box-hidden {
  transform: rotateY(180deg);
}

.mine-box-hidden img {
  animation: imageanimted 0.8s ease-in-out forwards;
  height: 100%;
  opacity: 0;
  transform: scale(1.4);
  width: 100%;
  object-fit: contain;
}

@keyframes imageanimted {
  50% {
    transform: scale(0.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* poker */
.card-item {
  display: flex;
  align-items: center;
  gap: 5px;
  /* border: 1px solid #dddddd26; */
  border-radius: 8px;
  /* margin-bottom: 5px; */
}
.card-item:last-child {
  margin-bottom: 8px;
}

.card-item__thumb img {
  max-width: 80px;
  max-height: 30px;
}
.card-item__text {
  color: #ed1569;
  display: flex;
  align-items: center;
}
.card-item .card-item__text {
  font-size: 12px;
  font-weight: 600;
}

/* poker table wrapper  */
.poker-table__wrapper {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
}
.poker-table {
  /* background-color: #020c25; */
  padding: 60px 50px;
  border-radius: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #fff;
  position: relative;
  z-index: 1;
  border-bottom: 0;
}
.poker-card .card-body {
  padding: 0;
}
@media screen and (max-width: 1199px) {
  .poker-table {
    padding: 50px 30px;
  }
}
.poker-table__thumb img {
  cursor: pointer;
  width: 100%;
  object-fit: cover;
}
.poker-table::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 33%;
  width: 41%;
  transform: translateY(-50%);
  background: #11c563;
  filter: blur(107px);
  z-index: -1;
  height: 61%;
}
.card.poker-card {
  padding: 40px 20px;
}
.card-item__icon {
  font-weight: 600;
  font-size: 14px;
}
.poker-card-table {
  position: relative;
}
.poker-card__bottom .cmn-btn {
  border-radius: 30px;
  z-index: 999;
  position: relative;
}
.poker-card__bottom {
  display: flex;
  gap: 10px;

  text-align: center;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
}
@media screen and (max-width: 767px) {
  .card-item {
    justify-content: center;
  }
}
@media screen and (max-width: 575px) {
  .poker-table__thumb {
    max-width: 70px;
  }
  .poker-table__wrapper {
    flex-wrap: wrap;
  }
}

.form-submit .btn--gradient-success {
  border-bottom: 4px solid hsl(var(--base-l-100));
  font-family: var(--heading-font);
  color: hsl(var(--dark)) !important;
  padding: 8px 24px !important;
  font-weight: 700;
  padding: 20px 24px !important;
  font-size: 32px;
  border: 0;
  background: hsl(var(--success-d-100));
  border-radius: 16px;
  position: relative;
  z-index: 1;
  border-radius: 12px;
  transition: all linear 0.4s;
}
.form-submit .btn--gradient-success::after {
  content: "";
  position: absolute;
  height: calc(100% - 10px);
  width: calc(100% - 10px);
  background: linear-gradient(179deg, #4fed0c, #295314c9);
  border-top: 0;
  top: 50%;
  left: 50%;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  opacity: 1;
  z-index: -1;
  transition: all linear 0.4s;
}
.btn--gradient-success:hover::after {
  opacity: 0.5;
}

.game--card {
  background-color: hsl(var(--black));
  border: 1px solid hsl(var(--white) / 0.1);
  border-top: 5px solid hsl(var(--base));
  border-radius: 12px;
  padding: 50px 25px;
  margin: 0 auto;
}

.form--control:disabled:focus,
.form--control[readonly]:focus {
  border: 1px solid hsl(var(--white) / 0.1);
}

.maintenance-page {
  display: grid;
  place-content: center;
  width: 100vw;
  height: 100vh;
}

.maintenance-icon {
  width: 60px;
  height: 60px;
  display: grid;
  place-items: center;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #fff;
  font-size: 26px;
  color: #e73d3e;
}

.input-group {
  border-radius: 12px !important;
  overflow: hidden;
}

.input-group-text {
  height: 56px;
  width: 60px;
  display: grid;
  place-content: center;
  color: hsl(var(--dark));
  flex-shrink: 0;
  background: var(--base-gradient);
  font-size: 18px;
  font-weight: 600;
}

/* @media (max-width: 576px) {
  .input-group-text {
    height: 48px;
  }
} */

@media (max-width: 425px) {
  .input-group-text {
    padding: 4px !important;
    font-size: 16px;
  }
}

.input-group .form--control {
  border-radius: 12px;
}

.minmax-btn {
  position: relative;
}

.minmax-btn:not(:last-child)::after {
  content: "";
  position: absolute;
  height: 20px;
  width: 2px;
  background-color: hsl(var(--black));
  top: 50%;
  transform: translateY(-50%);
  right: 0;
}
.policy {
  color: #000 !important;
}

/* ====================== */

.account-form__title {
  text-align: center;
  margin-bottom: 20px;
}

.account-form .social-list {
  justify-content: center;
}

.account-form__other {
  text-align: center;
  position: relative;
  margin: 16px 0;
}

.account-form__other span {
  width: 50px;
  background: hsl(var(--black));
  z-index: 1;
  position: relative;
}

.account-form__other-line {
  position: absolute !important;
  top: 50%;
  left: 0;
  border-bottom: 1px solid hsl(var(--white) / 0.1);
  width: 100% !important;
}

@media (max-width: 424px) {
  .account-form__other span {
    width: 50px;
  }
}
.social-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.social-list__item {
  margin-right: 10px;
}

.social-list__item:last-child {
  margin-right: 0;
}

.social-list__link {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  color: hsl(var(--white) / 0.4);
  background-color: #303a45;
  border: 2px solid rgba(255, 255, 255, 0.1);
  z-index: 1;
  transition: all linear 0.3s;
}

.social-list__link::after {
  content: "";
  position: absolute;
  height: calc(100% + 4px);
  width: calc(100% + 4px);
  background: var(--base-gradient);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  transition: all linear 0.3s;
  opacity: 0;
  border-radius: 50%;
}

.social-list__link.active,
.social-list__link:hover,
.social-list__link:focus {
  color: hsl(var(--dark)) !important;
}

.social-list__link.active::after,
.social-list__link:hover::after,
.social-list__link:focus::after {
  opacity: 1;
}

@media screen and (max-width: 767px) {
  .social-list__link {
    width: 35px;
    height: 35px;
    font-size: 0.875rem;
  }
}

.register-disable {
  height: 100vh;
  width: 100%;
  background-color: #fff;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
}

.register-disable-image {
  max-width: 300px;
  width: 100%;
  margin: 0 auto 32px;
}

.register-disable-title {
  color: rgb(0 0 0 / 80%);
  font-size: 42px;
  margin-bottom: 18px;
  text-align: center;
}

.register-disable-icon {
  font-size: 16px;
  background: rgb(255, 15, 15, 0.07);
  color: rgb(255, 15, 15, 0.8);
  border-radius: 3px;
  padding: 6px;
  margin-right: 4px;
}

.register-disable-desc {
  color: rgb(0 0 0 / 50%);
  font-size: 18px;
  max-width: 565px;
  width: 100%;
  margin: 0 auto 32px;
  text-align: center;
}

.register-disable-footer-link {
  color: #fff;
  background-color: #5b28ff;
  padding: 13px 24px;
  border-radius: 6px;
  text-decoration: none;
}

.register-disable-footer-link:hover {
  background-color: #440ef4;
  color: #fff;
}

.sound--btn {
  font-size: 20px;
  color: hsl(var(--white));
}
