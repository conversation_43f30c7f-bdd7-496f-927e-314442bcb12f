.list {
  display: flex;
  flex-direction: column;
  gap: var(--gap, 1rem);
  margin: 0;
  padding: 0;
  list-style: none;
  &--row {
      flex-direction: row;
  }
  &--base {
    li {
      position: relative;
      display: flex;
      &::before {
        content: "";
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 10px;
        height: 10px;
        flex-shrink: 0;
        line-height: 10px;
        border-radius: 50%;
        margin-right: 15px;
        position: relative;
        top: 8px;
        background: var(--base);
        box-shadow: 0 0 0 5px var(--shadow-alt);
      }
    }
  }
}