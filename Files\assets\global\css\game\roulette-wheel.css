.number:nth-child(1) {
  transform: rotateZ(9.7297297297297deg);
}

.number:nth-child(2) {
  transform: rotateZ(19.459459459459deg);
}

.number:nth-child(3) {
  transform: rotateZ(29.189189189189deg);
}

.number:nth-child(4) {
  transform: rotateZ(38.918918918919deg);
}

.number:nth-child(5) {
  transform: rotateZ(48.648648648649deg);
}

.number:nth-child(6) {
  transform: rotateZ(58.378378378378deg);
}

.number:nth-child(7) {
  transform: rotateZ(68.108108108108deg);
}

.number:nth-child(8) {
  transform: rotateZ(77.837837837838deg);
}

.number:nth-child(9) {
  transform: rotateZ(87.567567567568deg);
}

.number:nth-child(10) {
  transform: rotateZ(97.297297297297deg);
}

.number:nth-child(11) {
  transform: rotateZ(107.02702702703deg);
}

.number:nth-child(12) {
  transform: rotateZ(116.75675675676deg);
}

.number:nth-child(13) {
  transform: rotateZ(126.48648648649deg);
}

.number:nth-child(14) {
  transform: rotateZ(136.21621621622deg);
}

.number:nth-child(15) {
  transform: rotateZ(145.94594594595deg);
}

.number:nth-child(16) {
  transform: rotateZ(155.67567567568deg);
}

.number:nth-child(17) {
  transform: rotateZ(165.40540540541deg);
}

.number:nth-child(18) {
  transform: rotateZ(175.13513513514deg);
}

.number:nth-child(19) {
  transform: rotateZ(184.86486486486deg);
}

.number:nth-child(20) {
  transform: rotateZ(194.59459459459deg);
}

.number:nth-child(21) {
  transform: rotateZ(204.32432432432deg);
}

.number:nth-child(22) {
  transform: rotateZ(214.05405405405deg);
}

.number:nth-child(23) {
  transform: rotateZ(223.78378378378deg);
}

.number:nth-child(24) {
  transform: rotateZ(233.51351351351deg);
}

.number:nth-child(25) {
  transform: rotateZ(243.24324324324deg);
}

.number:nth-child(26) {
  transform: rotateZ(252.97297297297deg);
}

.number:nth-child(27) {
  transform: rotateZ(262.7027027027deg);
}

.number:nth-child(28) {
  transform: rotateZ(272.43243243243deg);
}

.number:nth-child(29) {
  transform: rotateZ(282.16216216216deg);
}

.number:nth-child(30) {
  transform: rotateZ(291.89189189189deg);
}

.number:nth-child(31) {
  transform: rotateZ(301.62162162162deg);
}

.number:nth-child(32) {
  transform: rotateZ(311.35135135135deg);
}

.number:nth-child(33) {
  transform: rotateZ(321.08108108108deg);
}

.number:nth-child(34) {
  transform: rotateZ(330.81081081081deg);
}

.number:nth-child(35) {
  transform: rotateZ(340.54054054054deg);
}

.number:nth-child(36) {
  transform: rotateZ(350.27027027027deg);
}

.main {
  width: 374px;
  margin: 0 auto;
}

.plate {
  background-color: #492e9b;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  position: relative;
  /*animation: rotate 24s infinite linear;*/
  /*-webkit-animation: rotate 24s infinite linear;*/
  display: inline-block;
  text-align: left;
}

.roulette-game-spin.spin-active .plate {
  animation: rotate 24s infinite linear;
  -webkit-animation: rotate 24s infinite linear;
}

@media (max-width: 1199px) {
  .plate {
    text-align: left;
  }
}

.plate:after,
.plate:before {
  content: '';
  display: block;
  position: absolute;
  border-radius: 50%;
}

.plate:after {
  top: -6px;
  right: -6px;
  bottom: -6px;
  left: -6px;
  border: 6px solid #653c9b;
  box-shadow: inset 0px 0px 0px 2px #7946a3, 0px 0px 0px 2px #623a9b;
}

.plate:before {
  background: #5e389b80;
  border: 1px solid #8c51ad;
  box-shadow: inset 0px 0px 0px 2px #963dd8;
  top: 12%;
  left: 12%;
  right: 12%;
  bottom: 12%;
  z-index: 1;
}

.number {
  width: 21px;
  height: 124px;
  display: inline-block;
  text-align: center;
  position: absolute;
  top: 0;
  left: calc(45%);
  transform-origin: 44% 100%;
  background-color: transparent;
  border-left: 14px solid transparent;
  border-right: 12px solid transparent;
  border-top: 94px solid black;
  box-sizing: border-box;
}

.number:nth-child(odd) {
  border-top-color: red;
}

.number:nth-child(37) {
  border-top-color: green;
}

.pit {
  color: #fff;
  padding-top: 12px;
  width: 32px;
  display: inline-block;
  font-size: 9px;
  transform: scale(1, 1.8);
  position: absolute;
  top: -105px;
  left: -19px;
}

.inner {
  display: block;
  height: 250px;
  width: 250px;
  position: relative;
}

.inner:after,
.inner:before {
  content: '';
  display: block;
  position: absolute;
  border-radius: 50%;
}

.inner:after {
  z-index: 3;
  top: 24%;
  right: 24%;
  bottom: 24%;
  left: 24%;
  background-color: #4d309a;
  border: 3px solid #412a9e;
}

.inner:before {
  top: 24%;
  bottom: 21%;
  left: 24%;
  right: 22%;
  content: '';
  color: #fff;
  font-size: 42px;
  z-index: 5;
  border-radius: 0;
}

.inner[data-spinto='1']:before {
  transform: rotateZ(-2590deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='2']:before {
  transform: rotateZ(-2760deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='3']:before {
  transform: rotateZ(-2835deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='4']:before {
  transform: rotateZ(-2780deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='5']:before {
  transform: rotateZ(-2630deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='6']:before {
  transform: rotateZ(-2722deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='7']:before {
  transform: rotateZ(-2516deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='8']:before {
  transform: rotateZ(-2644deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='9']:before {
  transform: rotateZ(-2552deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='10']:before {
  transform: rotateZ(-2639deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='11']:before {
  transform: rotateZ(-2684deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='12']:before {
  transform: rotateZ(-2855deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='13']:before {
  transform: rotateZ(-2701deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='14']:before {
  transform: rotateZ(-2566deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='15']:before {
  transform: rotateZ(-2799deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='16']:before {
  transform: rotateZ(-2609deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='17']:before {
  transform: rotateZ(-2739deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='18']:before {
  transform: rotateZ(-2533deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='19']:before {
  transform: rotateZ(-2789deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='20']:before {
  transform: rotateZ(-2576deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='21']:before {
  transform: rotateZ(-2768deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='22']:before {
  transform: rotateZ(-2538deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='23']:before {
  transform: rotateZ(-2651deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='24']:before {
  transform: rotateZ(-2620deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='25']:before {
  transform: rotateZ(-2748deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='26']:before {
  transform: rotateZ(-2824deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='27']:before {
  transform: rotateZ(-2711deg);
  content: '\2022';
  transition: transform 9s ease-out;
}

.inner[data-spinto='28']:before {
  transform: rotateZ(-2861deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='29']:before {
  transform: rotateZ(-2523deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='30']:before {
  transform: rotateZ(-2673deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='31']:before {
  content: '\2022';
  transition: transform 9s ease-out;
  transform: rotateZ(-2564deg);
}

.inner[data-spinto='32']:before {
  transform: rotateZ(-2806deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='33']:before {
  transform: rotateZ(-2596deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='34']:before {
  transform: rotateZ(-2731deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='35']:before {
  transform: rotateZ(-2483deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='36']:before {
  transform: rotateZ(-2687deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner[data-spinto='0']:before {
  transform: rotateZ(-2815deg);
  transition: transform 9s ease-out;
  content: '\2022';
}

.inner.rest:before {
  transition: top .5s ease-in, right .5s ease-in, bottom .5s ease-in, left .5s ease-in;
  top: 25%;
  right: 25%;
  bottom: 24%;
  left: 25%;
}

.btn {
  margin: 3px 5px;
  cursor: pointer;
  color: #fff;
}

.btn .btn-label {
  padding: 12px;
  white-space: nowrap;
}

.btn.disabled {
  opacity: .2;
  transition: opacity .24s linear;
  cursor: not-allowed;
}

@keyframes rotate {
  0% {
    transform: rotateZ(0deg);
  }

  100% {
    transform: rotateZ(360deg);
  }
}

.data {
  display: block;
  position: absolute;
  top: 30%;
  right: 30%;
  bottom: 30%;
  left: 30%;
  border-radius: 50%;
  perspective: 2000px;
  z-index: 100;
  /*animation: rotate 24s reverse linear infinite;*/
  /*-webkit-animation: rotate 24s reverse linear infinite; */
}

.data .data-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.72s;
  transform-style: preserve-3d;

}

.data.reveal .data-inner {
  transform: rotateY(180deg);
}

.data .mask,
.data .result {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: absolute;
  backface-visibility: hidden;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.data .mask {
  color: #fff;
  font-size: 16px;
  margin: auto;
  line-height: 1.4;
}

.data .result {
  background-color: green;
  color: white;
  transform: rotateY(180deg);
  align-items: center;
  color: #fff;
}

.data .result-number {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.2;
}

.data .result-color {
  text-transform: uppercase;
  font-size: 14px;
  line-height: 1;
}

.previous-results {
  max-width: 350px;
  margin: 24px 12px;
}

.previous-list {
  perspective: 2000;
}

.previous-result {
  display: flex;
  margin: 1px;
  padding: 12px;
  color: #fff;
}

.previous-result:first-child {
  animation: flipin .48s ease-out;
}

.previous-result .previous-number {
  flex: 1;
}

.color-green {
  background-color: green;
}

.color-black {
  background-color: black;
}

.color-red {
  background-color: red;
}

.visuallyhidden {
  position: absolute;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
}

@keyframes flipin {
  0% {
    transform: rotateX(90deg);
  }

  100% {
    transform: rotateX(0deg);
  }
}