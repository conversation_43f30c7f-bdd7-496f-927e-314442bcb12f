@keyframes fly-in {
  0% {
    transform: rotateY(540deg) rotateX(1080deg) translateZ(400px) translateX(80px) translateY(0);
  }
  40% {
    transform: rotateY(0deg) rotateX(0) translateZ(0) translateX(0) translateY(0);
  }
  60% {
    transform: rotateY(90deg) rotateX(0) translateZ(0) translateX(100px) translateY(1000px);
  }
  80% {
    transform: rotateY(180deg) rotateX(0) translateZ(0) translateX(0) translateY(0);
  }
  100% {
    transform: rotateY(0) rotateX(0) translateZ(0) translateX(0) translateY(0);
  }
}

@keyframes fly-in-res {
  0% {
    transform: rotateY(180deg) rotateX(150deg);
  }
  40% {
    transform: rotateY(180deg) rotateX(150deg);
  }
  60% {
    transform: rotateY(180deg) rotateX(150deg);
  }
  100% {
    transform: rotateY(360deg) rotateX(360deg);
  }
}
.card-holder{
  animation-name:fly-in;
  animation-duration:7s;
  animation-direction:alternate;
  animation-iteration-count:infinite;
  animation-timing-function:ease-in-out;
}
.res{
  animation-name:fly-in-res;
  animation-duration:5s;
  animation-direction: reverse;
  animation-fill-mode: forwards;
}
.card-holder:nth-of-type(1){
  animation-delay:.5s;
}
.card-holder:nth-of-type(2){
  animation-delay:1s;
}
.card-holder:nth-of-type(3){
  animation-delay:1.5s;
}
.card-holder:nth-of-type(4){
  animation-delay:2s;
}
.card-holder:nth-of-type(5){
  animation-delay:2.5s;
}
.card-holder:nth-of-type(6){
  animation-delay:3s;
}
.card-holder:nth-of-type(7){
  animation-delay:3.5s;
}
.card-holder:nth-of-type(8){
  animation-delay:4s;
}
.card-holder:nth-of-type(9){
  animation-delay:4.5s;
}
.card-holder:nth-of-type(10){
  animation-delay:5s;
}
.card-holder:nth-of-type(11){
  animation-delay:5.5s;
}
.card-holder:nth-of-type(12){
  animation-delay:6s;
}
.card-holder:nth-of-type(13){
  animation-delay:6.5s;
}
.card-holder:nth-of-type(14){
  animation-delay:7s;
}
.card-holder:nth-of-type(15){
  animation-delay:7.5s;
}
.card-holder:nth-of-type(16){
  animation-delay:8s;
}
.card-holder:nth-of-type(17){
  animation-delay:8.5s;
}
.card-holder:nth-of-type(18){
  animation-delay:9s;
}
.card-holder:nth-of-type(19){
  animation-delay:9.5s;
}
.card-holder:nth-of-type(20){
  animation-delay:10s;
}
.card-holder:nth-of-type(21){
  animation-delay:10.5s;
}
.card-holder{
  transform-style: preserve-3d;
  backface-visibility: hidden;
  position:relative;
  width:99px;
  height:140px;
  display:inline-block;
  box-shadow:1px 2px 2px rgba(0,0,0,.8);
  margin:2px;
}
.card-holder:hover {
  box-shadow:0px 3px 6px rgba(0,0,0,.7);
}
.card-holder:active{
  transform:scale(.9);
}

.res{
    transform-style: preserve-3d;
    backface-visibility: hidden;
    position: relative;
    width: 160px;
    height: 144px;
    display: inline-block;
    margin: 2px;
    margin: 0 auto;
    margin-left: 39%;
    margin-top: 25%;
    z-index: 999;
    border: none;
}

.res:hover {
  box-shadow:0px 3px 6px rgba(0,0,0,.7);
}

.res:active{
  transform:scale(.9);
}

.flying-card {
  box-shadow: inset 2px 2px 0 #fff, inset -2px -2px 0 #fff;
  transform-style: preserve-3d;
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  right:0;
  backface-visibility: hidden;
  background-color:#fcfcfc;
  border-radius:2%;
  display:block;
  width:100%;
  height:100%;
  border:1px solid black;
}

.inpCard{
    cursor: pointer;
}

.res .flying-card {
     box-shadow: none; 
     border: none; 
}
@media screen and (max-width:1199px)  and (min-width:992px) {
    .game-details-right {
      padding: 30px 30px;
    }
}