.c-point {
    cursor: pointer;
}

table {
    border-collapse: separate;
    border-spacing: 2px;
    margin: 0 auto;
}

table tr {
    color: white;
}

table tr td {
    border-radius: 10px;
    cursor: pointer;
    transition: all .3s;
    padding: 10px 25px;
    border: 1px solid #ffffff85;
    position: relative;
    text-align: center;
    line-height: 1;
    color: #fff;
    font-size: 14px;
}

table .btn-color {
    color: #fff;
}

.dark table .btn-color {
    color: #fff;
}

table tr td input {
    background: transparent;
    border: 0px;
    font-weight: bold;
}

table tr td:hover {
    opacity: 0.5;
}

@media (max-width: 1199px) {
    table {
        width: 100%;
    }

    table tr td {
        padding: 10px 20px;
    }
}

@media (max-width: 991px) {
    table {
        width: 100%;
    }

    table tr td {
        padding: 10px 12px;
    }
}

@media (max-width: 768px) {
    table {
        width: 100%;
    }

    table tr td {
        padding: 20px;
    }
}

@media (max-width: 640px) {
    table {
        border-spacing: 3px;
    }

    table tr td {
        padding: 18px 20px !important;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    table tr td {
        padding: 15px 20px !important;
        font-size: 16px;
        border-radius: 5px;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        -ms-border-radius: 5px;
        -o-border-radius: 5px;
    }
}

@media (max-width: 380px) {
    table tr td {
        padding: 16px 18px !important;
        font-size: 14px;
    }
}

.oneToTw button {
    background: transparent;
    border-radius: 7px;
}

.thrtToTf button {
    background: transparent;
    border-radius: 7px;
}

.twfToTs button {
    background: transparent;
    border-radius: 7px;
}

.oneToEt button {
    background: transparent;
    border-radius: 7px;
}

.nineteenTtsix button {
    background: transparent;
    border-radius: 7px;
}

.even button {
    background: transparent;
    border-radius: 7px;
}

.odd button {
    background: transparent;
    border-radius: 10px;
}

.twByOne1 button {
    background: transparent;
    border-radius: 10px;
    /* padding: 15px 20px; */
}

.twByOne2 button {
    background: transparent;
    border-radius: 10px;
    /* padding: 15px 20px; */
}

.twByOne3 button {
    background: transparent;
    border-radius: 10px;
    /* padding: 15px 20px; */
}


button:focus {

    outline: 0px dotted;
    outline: 0px auto -webkit-focus-ring-color;
}

.table td {
    vertical-align: middle;
}

.bg-dark {
    background-color: #000000b8 !important;
}

.__select {
    opacity: 0.7;
}

.selected {
    opacity: 0.7;
}

.custom-badge {
    padding: 2px 2px;
    font-size: 12px;
    position: absolute;
    top: -3px;
    left: 2px;
    font-weight: 400;
}

@media (max-width: 640px) {
    .custom-badge {
        font-size: 8px;
    }
}

.buttons .btn-primary {
    color: #000;
    background-color: #fff;
    border-color: #fff;
    padding: 6px 25px;
    font-size: 20px;
}

.buttons .btn-primary:hover,
.buttons .btn-primary:active,
.buttons .btn-primary:focus {
    background-color: #531bc8 !important;
    border-color: #531bc8 !important;
    color: #fff;
}

.bg-danger {
    background-color: #FF2424 !important;
}
 table tr td {
    word-break: normal;
}


.roulette-game-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    overflow: hidden;
}

.roulette-game-spin {
    text-align: center;
}

@media (max-width: 1260px) {
    .roulette-game-spin {
        width: 260px;
    }
}

@media (max-width: 1199px) {
    .roulette-game-spin {
        width: 100%;
        text-align: center;
    }
}

.numbers td.bg-transparent {
    background-color: transparent;
    color: #fff;
}

@media (max-width: 767px) {
    .roulette-wrapper {
        height: 830px !important;
        position: relative;
    }
}

@media (max-width: 767px) {
    .roulette-wrapper table {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%) rotate(90deg);
        -ms-transform: translate(-50%, -50%) rotate(90deg);
        transform: translate(-50%, -50%) rotate(90deg);
    }
}