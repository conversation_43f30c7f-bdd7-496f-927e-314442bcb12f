@media (max-width: 1500px) {
  .coins {
    margin-top: 11px;
  }
}

@media (max-width: 1300px) {
  .coins {
    margin-top: 17px;
  }
}

@media (max-width: 991px) {
  .coins {
    margin-top: 0;
  }

  #coin {
    width: 300px !important;
  }

  #coin {
    position: relative;
    width: 300px !important;
    transform-style: preserve-3d;
  }

  #coin .front,
  #coin .back {
    width: 300px !important;
    height: 300px !important;
  }

  .flp {
    height: 300px;
  }
}

@media (max-width: 767px) {
  .coins {
    margin-top: -11px;
  }
}

@media (max-width: 600px) {
  .coins {
    margin-top: 2px;
  }
}

@media (max-width: 574px) {
  .coins {
    width: 55%;
    margin: 0 auto;
    text-align: center;
  }
}

@media (max-width: 1199px) {
  #coin {
    position: relative;
    width: 250px;
    transform-style: preserve-3d;
  }

  #coin .front,
  #coin .back {
    position: absolute;
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 1110px) {
  #coin {
    position: relative;
    width: 200px;
    transform-style: preserve-3d;
  }

  #coin .front,
  #coin .back {
    position: absolute;
    width: 200px;
    height: 200px;
  }
}

@media (max-width: 330px) {
  #coin {
    position: relative;
    width: 200px !important;
    transform-style: preserve-3d;
  }

  #coin .front,
  #coin .back {
    position: absolute;
    width: 200px !important;
    height: 200px !important;
  }
}

#coin-flip-cont {
  width: 300px;
  height: 300px;
}

#coin {
  position: relative;
  width: 300px;
  transform-style: preserve-3d;
}

#coin .front,
#coin .back {
  position: absolute;
  width: 300px;
  height: 300px;
}

#coin .front {
  transform: translateZ(1px);
}

#coin .back {
  transform: translateZ(-1px) rotateY(180deg);
}

#coin.animate {
  -webkit-animation: rotate2160 7s linear infinite;
  animation: rotate2160 7s linear infinite;
}

#coin.animateClick {
  -webkit-animation: rotate18000 15.2s linear forwards;
  animation: rotate18000 15.2s linear forwards;
}

@media (max-width: 340px) {
  #coin-flip-cont {
    width: 200px;
    height: 200px;
  }

  #coin {
    width: 200px !important;
  }

  #coin .front,
  #coin .back {
    width: 200px !important;
    height: 200px !important;
  }

  .flp {
    height: 200px;
  }
}

@-webkit-keyframes rotate900 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(900deg);
    -moz-transform: rotateY(900deg);
    transform: rotateY(900deg);
  }
}

@keyframes rotate900 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(900deg);
    -moz-transform: rotateY(900deg);
    transform: rotateY(900deg);
  }
}

@-webkit-keyframes rotate1080 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1080deg);
    -moz-transform: rotateY(1080deg);
    transform: rotateY(1080deg);
  }
}

@keyframes rotate1080 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1080deg);
    -moz-transform: rotateY(1080deg);
    transform: rotateY(1080deg);
  }
}

@-webkit-keyframes rotate1260 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1260deg);
    -moz-transform: rotateY(1260deg);
    transform: rotateY(1260deg);
  }
}

@keyframes rotate1260 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1260deg);
    -moz-transform: rotateY(1260deg);
    transform: rotateY(1260deg);
  }
}

@-webkit-keyframes rotate1440 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1440deg);
    -moz-transform: rotateY(1440deg);
    transform: rotateY(1440deg);
  }
}

@keyframes rotate1440 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1440deg);
    -moz-transform: rotateY(1440deg);
    transform: rotateY(1440deg);
  }
}

@-webkit-keyframes rotate1620 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1620deg);
    -moz-transform: rotateY(1620deg);
    transform: rotateY(1620deg);
  }
}

@keyframes rotate1620 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1620deg);
    -moz-transform: rotateY(1620deg);
    transform: rotateY(1620deg);
  }
}

@-webkit-keyframes rotate1800 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1800deg);
    -moz-transform: rotateY(1800deg);
    transform: rotateY(1800deg);
  }
}

@keyframes rotate1800 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1800deg);
    -moz-transform: rotateY(1800deg);
    transform: rotateY(1800deg);
  }
}

@-webkit-keyframes rotate1980 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1980deg);
    -moz-transform: rotateY(1980deg);
    transform: rotateY(1980deg);
  }
}

@keyframes rotate1980 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(1980deg);
    -moz-transform: rotateY(1980deg);
    transform: rotateY(1980deg);
  }
}

@-webkit-keyframes rotate2160 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(2160deg);
    -moz-transform: rotateY(2160deg);
    transform: rotateY(2160deg);
  }
}

@keyframes rotate2160 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(2160deg);
    -moz-transform: rotateY(2160deg);
    transform: rotateY(2160deg);
  }
}

@-webkit-keyframes rotate18000 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(18000deg);
    -moz-transform: rotateY(18000deg);
    transform: rotateY(18000deg);
  }
}

@keyframes rotate18000 {
  from {
    -webkit-transform: rotateY(0);
    -moz-transform: rotateY(0);
    transform: rotateY(0);
  }

  to {
    -webkit-transform: rotateY(18000deg);
    -moz-transform: rotateY(18000deg);
    transform: rotateY(18000deg);
  }
}

@media (max-width: 575px) {
  .gmimg {
    max-width: 50% !important;
  }
}

@media (max-width: 400px) {
  .gmimg {
    max-width: 80% !important;
  }
}

/*New CSS*/
.coins-wrapper {
  position: relative;
  transform-style: preserve-3d;
  -webkit-transition: all ease 0.3s;
  -moz-transition: all ease 0.3s;
  transition: all ease 0.3s;
  width: 300px;
  height: 300px;
  animation: coin-rotate 2s linear infinite;
}

@media (max-width: 374px) {
  .coins-wrapper {
    width: 200px;
  }
}

.coins-wrapper:hover {
  transform: rotateY(180deg);
}

.coins-wrapper .front {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.coins-wrapper .back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  transform: rotateY(180deg);
}

@keyframes coin-rotate {
  0% {
    transform: rotateY(0);
  }

  100% {
    transform: rotateY(360deg);
  }
}
