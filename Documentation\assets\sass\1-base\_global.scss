@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Ubuntu:wght@400;500;700&display=swap');
:root {
  // TypoGraphy
  --heading-font: "Inter", sans-serif;
  --body-font: 'Ubuntu', sans-serif;

  --d1: clamp(3.5rem, 6vw + 1rem, 6.25rem); //Max: 100px
  --h1: clamp(2.8125rem, 4vw + 1rem, 4.209rem); //Min:45px - Max:67.34px
  --h2: clamp(2.0625rem, 3vw + 1rem, 3.1575rem); //Min: 33px - Max: 50.52px
  --h3: clamp(1.875rem, 2.7vw + 1rem, 2.369rem); //Min: 30px - Max: 37.90px
  --h4: clamp(1.4rem, 2vw + 1rem, 1.777rem); //28.43px
  --h5: 1.333rem; //21.33px
  --h6: 1rem;

  // Base Colors
  --base: #071251; 
  // Accent Color Shade 
  --accent: #062C4E;
  --canvas: #4634ff;
  --shadow: #136e8a28;
  --shadow-alt: #062c4e25;
  // Black Color Shade 
  --dark-h: 229;
  --dark-s: 54%;
  --dark-l: 8%;
  --dark-100: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 90%);
  --dark-200: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 80%);
  --dark-300: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 70%);
  --dark-400: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 60%);
  --dark: var(--dark-h) var(--dark-s) var(--dark-l);
  --dark-600: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 50%);
  --dark-700: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 40%);
  --dark-800: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 30%);
  --dark-900: var(--dark-h) var(--dark-s) calc(var(--dark-l) + 20%);
  // White Color 
  --white: 0 0% 100%;
  --light: 200 43% 99%;
  --border: 0 0% 88%;

  --primary: 199 100% 60%;
  --secondary: 210 11% 71%;
  --success: 159 83% 45%;
  --danger: 358 91% 66%;
  --warning: 29 100% 63%;
  --info: 188 92% 45%;
  --dark: 225 23% 17%;

}

html {
  font-size: 16px;
}

body {
  position: relative;
  background: hsl(var(--white));
  font-family: var(--body-font);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: hsl(var(--dark));
}

// Typography
p {
  margin-bottom: 1.5rem;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 1.5rem 0 1rem;
  font-family: var(--heading-font);
  font-weight: 700;
  line-height: 1.15;
  color: hsl(var(--dark));
  
}

h1 {
  margin-top: 0;
  font-size: var(--h1);
}

h2 {
  font-size: var(--h2);
}

h3 {
  font-size: var(--h3);
}

h4 {
  font-size: var(--h4);
}

h5 {
  font-size: var(--h5);
}
h6 {
  font-size: var(--h6);
  letter-spacing: .05em;
}
.xsm-text {
  font-size: 12px;
}
small,
.sm-text {
  font-size: 14px;
}
.lg-text {
  font-size: 18px;
}
.xl-text {
  font-size: 20px;
}
.xxl-text {
  font-size: 24px;
}
.fw-regular {
  font-weight: 400;
}
.fw-md {
  font-weight: 500;
}
.lh-1 {
  line-height: 1;
}
.hr {
  background-color: hsl(var(--base));
}

// Button Default
button {
  &:focus {
    outline: none !important;
  }
}
.btn:focus,
.btn.focus {
  outline: none;
  box-shadow: none;
}

// Form Control Default
input:focus {
  outline: none;
}
.form-control {
  border: 1px solid hsl(var(--dark)/.1);
}

textarea {
  resize: none;
}

.container-restricted {
  @include xliv-screen {
    max-width: 1540px;
    margin-left: auto;
    margin-right: auto;
  }
}
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
