{"Welcome to": "Welcome to", "to": "to", "Dashboard": "Dashboard", "Username": "Username", "Password": "Password", "Forgot Password?": "Forgot Password?", "LOGIN": "LOGIN", "Verify Code": "Verify Code", "Please check your email and enter the verification code you got in your email.": "Please check your email and enter the verification code you got in your email.", "Verification Code": "Verification Code", "Submit": "Submit", "Try to send again": "Try to send again", "Back to Login": "Back to Login", "Recover Account": "Recover Account", "Email": "Email", "New Password": "New Password", "Re-type New Password": "Re-type New Password", "Name": "Name", "Schedule": "Schedule", "Next Run": "Next Run", "Last Run": "Last Run", "Is Running": "Is Running", "Type": "Type", "Actions": "Actions", "Running": "Running", "Pause": "Pause", "Default": "<PERSON><PERSON><PERSON>", "Customizable": "Customizable", "Action": "Action", "Run Now": "Run Now", "Play": "Play", "Edit": "Edit", "Logs": "Logs", "Are you sure to delete this cron?": "Are you sure to delete this cron?", "Delete": "Delete", "Add Cron Job": "<PERSON>d <PERSON>", "Edit Cron Job": "<PERSON> <PERSON><PERSON>", "Add": "Add", "Cron Schedule": "Cron Schedule", "Start At": "Start At", "End At": "End At", "Execution Time": "Execution Time", "Error": "Error", "Are you sure to resolved this log?": "Are you sure to resolved this log?", "Resolved": "Resolved", "Are you sure to flush all logs?": "Are you sure to flush all logs?", "Flush Logs": "Flush Logs", "Interval": "Interval", "Status": "Status", "Seconds": "Seconds", "Are you sure to enable this schedule?": "Are you sure to enable this schedule?", "Enable": "Enable", "Are you sure to disable this schedule?": "Are you sure to disable this schedule?", "Disable": "Disable", "Add Cron Schedule": "Add Cron Schedule", "Add New": "Add New", "Update Schedule": "Update Schedule", "Add Schedule": "Add Schedule", "Deposits": "Deposits", "Total Deposited": "Total Deposited", "Pending Deposits": "Pending Deposits", "Rejected Deposits": "Rejected Deposits", "Deposited Charge": "Deposited Charge", "Withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "Total Withdrawn": "Total Withdrawn", "Pending Withdrawals": "Pending Withdrawals", "Rejected Withdrawals": "Rejected <PERSON>s", "Withdrawal Charge": "Withdrawal Charge", "Deposit & Withdraw Report": "Deposit & Withdraw Report", "Transactions Report": "Transactions Report", "Login By Browser": "Login <PERSON>rowser", "Last 30 days": "Last 30 days", "Login By OS": "Login By OS", "Login By Country": "Login By Country", "Cron Setup": "<PERSON><PERSON>", "Deposit Via": "Deposit Via", "Google Pay": "Google Pay", "Date": "Date", "Transaction Number": "Transaction Number", "Method": "Method", "Amount": "Amount", "Charge": "Charge", "After Charge": "After Charge", "Rate": "Rate", "After Rate Conversion": "After Rate Conversion", "Admin Response": "Admin Response", "User Deposit Information": "User Deposit Information", "Attachment": "Attachment", "No File": "No File", "Are you sure to approve this transaction?": "Are you sure to approve this transaction?", "Approve": "Approve", "Reject": "Reject", "Reject Deposit Confirmation": "Reject Deposit Confirmation", "Are you sure to": "Are you sure to", "reject": "reject", "deposit of": "deposit of", "Reason for Rejection": "Reason for Rejection", "Gateway | Transaction": "Gateway | Transaction", "Initiated": "Initiated", "User": "User", "Conversion": "Conversion", "charge": "charge", "Amount with charge": "Amount with charge", "Details": "Details", "Successful Deposit": "Successful Deposit", "Pending Deposit": "Pending Deposit", "Rejected Deposit": "Rejected De<PERSON>t", "Initiated Deposit": "Initiated <PERSON><PERSON><PERSON><PERSON>", "Extension": "Extension", "Configure": "Configure", "Help": "Help", "Are you sure to enable this extension?": "Are you sure to enable this extension?", "Are you sure to disable this extension?": "Are you sure to disable this extension?", "Update Extension": "Update Extension", "Script": "<PERSON><PERSON><PERSON>", "Paste your script with proper key": "Paste your script with proper key", "Need Help": "Need Help", "Search": "Search", "Page Name": "Page Name", "Make Slug": "Make Slug", "Page Slug": "<PERSON>", "Page": "Page", "You\\'ve to click on the Update Now button to apply the changes": "You\\'ve to click on the Update Now button to apply the changes", "Drag & drop your section here": "Drag & drop your section here", "Update Now": "Update Now", "Sections": "Sections", "Drag the section to the left side you want to show on the page.": "Drag the section to the left side you want to show on the page.", "Verifying": "Verifying", "Verified": "Verified", "Slug already exists": "Slug already exists", "Slug": "Slug", "SEO Setting": "SEO Setting", "Are you sure to remove this page?": "Are you sure to remove this page?", "Add New Page": "Add <PERSON> Page", "The SEO setting is optional for this page. If you don\\'t configure SEO here, the global SEO contents will work for this page, which you can configure from": "The SEO setting is optional for this page. If you don\\'t configure SEO here, the global SEO contents will work for this page, which you can configure from", "SEO Image": "SEO Image", "Meta Keywords": "Meta Keywords", "Separate multiple keywords by": "Separate multiple keywords by", "comma": "comma", "or": "or", "enter": "enter", "key": "key", "Meta Description": "Meta Description", "Social Title": "Social Title", "Social Description": "Social Description", "Content Management Options": "Content Management Options", "No notification found.": "No notification found.", "SL": "SL", "Image": "Image", "Are you sure to remove this item?": "Are you sure to remove this item?", "Remove": "Remove", "Item": "<PERSON><PERSON>", "Update": "Update", "Select One": "Select One", "Import": "Import", "SELECTED": "SELECTED", "SELECT": "SELECT", "Get This": "Get This", "Game Name": "Game Name", "Play Amount": "Play Amount", "Minimum Invest Amount": "Minimum Invest Amount", "Maximum Invest Amount": "Maximum Invest Amount", "Win Chance": "Win Chance", "Win Commission": "Win Commission", "Game App Short Description": "Game App Short Description", "Game Instruction": "Game Instruction", "For App": "For App", "Trending": "Trending", "Yes": "Yes", "No": "No", "Featured": "Featured", "Win Setting": "Win Setting", "Winning Chance": "Winning Chance", "%": "%", "Win Amount Per Mines": "Win Amount Per Mines", "Win Amount": "Win Amount", "Win": "Win", "Invest": "Invest", "Give Back": "Give Back", "No Back\"": "No Back\"", "Minimum Invest": "Minimum Invest", "Maximum Invest": "Maximum Invest", "Are you sure to enable this game?": "Are you sure to enable this game?", "Are you sure to disable this game?": "Are you sure to disable this game?", "Bet Amount & Number": "Bet Amount & Number", "How many number user can select": "How many number user can select", "This number means how many numbers a user can select from the Keno number plate and you have to put a number above 3, mainly the Keno game provides 10": "This number means how many numbers a user can select from the Keno number plate and you have to put a number above 3, mainly the Keno game provides 10", "Qty": "Qty", "Win Bonus": "Win Bonus", "When the user invests in Keno he selected some number. If the numbers selected by him and match any of the following levels, he will get the commission bonus on the invested amount": "When the user invests in <PERSON><PERSON> he selected some number. If the numbers selected by him and match any of the following levels, he will get the commission bonus on the invested amount", "Match": "Match", "number get": "number get", "Commission Percentage": "Commission Percentage", "User Select": "User Select", "Result": "Result", "Win or fail": "Win or fail", "N/A": "N/A", "Loss": "Loss", "All": "All", "Current Setting": "Current Setting", "Number of Mines": "Number of Mines", "Commision": "Commision", "MINES#": "MINES#", "Change Setting": "Change Setting", "Type a number & hit ENTER ↵": "Type a number & hit ENTER ↵", "Generate": "Generate", "Please enter a number": "Please enter a number", "Mines & Bonus old data will remove after generate": "Mines & Bonus old data will remove after generate", "Mines": "Mines", "Chance": "Chance", "CHANCE#": "CHANCE#", "Number of Chances": "Number of Chances", "Chance & Bonus old data will remove after generate": "Chance & Bonus old data will remove after generate", "Win Chance Setting": "Win Chance Setting", "No Win Chance": "No Win Chance", "Single Win Chance": "Single Win Chance", "Double Win Chance": "Double Win Chance", "Triple Win Chance": "Triple Win Chance", "Win Bonus Setting": "Win Bonus Setting", "Single Win Bonus": "Single Win Bonus", "Double Win Bonus": "Double Win Bonus", "Triple Win Bonus": "Triple Win Bonus", "Poker Hand Ranking": "Poker Hand Ranking", "RANK#": "RANK#", "Number of Rank": "Number of Rank", "Rank & Bonus old data will remove after generate": "Rank & Bonus old data will remove after generate", "Rank": "Rank", "Select currency": "Select currency", "No available currency support": "No available currency support", "Add new": "Add new", "Configurations": "Configurations", "Copy": "Copy", "Set the URL to your server\\'s cron job to validate the payment. You can also set the cron job to the system\\'s": "Set the URL to your server\\'s cron job to validate the payment. You can also set the cron job to the system\\'s", "Cron Job Manager": "Cron Job Manager", "Global Setting for": "Global Setting for", "Are you sure to delete this gateway currency?": "Are you sure to delete this gateway currency?", "Range": "Range", "Minimum Amount": "Minimum Amount", "Maximum Amount": "Maximum Amount", "Fixed Charge": "Fixed Charge", "Percent Charge": "Percent Charge", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Symbol": "Symbol", "Configuration": "Configuration", "Minimum amount field is required": "Minimum amount field is required", "Maximum amount field is required": "Maximum amount field is required", "Minimum amount should be less than maximum amount": "Minimum amount should be less than maximum amount", "Gateway": "Gateway", "Supported Currency": "Supported <PERSON><PERSON><PERSON><PERSON>", "Enabled Currency": "Enabled <PERSON><PERSON><PERSON>cy", "Are you sure to enable this gateway?": "Are you sure to enable this gateway?", "Are you sure to disable this gateway?": "Are you sure to disable this gateway?", "Gateway Name": "Gateway Name", "Deposit Instruction": "Deposit Instruction", "You\\'ve to click on the submit button to apply the changes": "You\\'ve to click on the submit button to apply the changes", "User Data": "User Data", "Automatic Gateway": "Automatic Gateway", "Manual Gateway": "Manual Gateway", "KYC Form for User": "KYC Form for User", "Language Keywords of": "Language Keywords of", "Add New Key": "Add New Key", "Key": "Key", "Value": "Value", "Confirmation Alert!": "Confirmation Alert!", "Are you sure to delete this key from this language?": "Are you sure to delete this key from this language?", "Import Keywords": "Import Keywords", "Import From": "Import From", "System": "System", "Close": "Close", "Import Now": "Import Now", "While you are adding a new keyword, it will only add to this current language only. Please be careful on entering a keyword, please make sure there is no extra space. It needs to be exact and case-sensitive.": "While you are adding a new keyword, it will only add to this current language only. Please be careful on entering a keyword, please make sure there is no extra space. It needs to be exact and case-sensitive.", "Code": "Code", "Selectable": "Selectable", "Translate": "Translate", "Are you sure to remove this language from this system?": "Are you sure to remove this language from this system?", "Add New Language": "Add New Language", "Flag": "Flag", "Language Name": "Language Name", "Language Code": "Language Code", "Default Language": "Default Language", "SET": "SET", "UNSET": "UNSET", "Edit Language": "Edit Language", "Language Keywords": "Language Keywords", "All of the possible language keywords are available here. However, some keywords may be missing due to variations in the database. If you encounter any missing keywords, you can add them manually.": "All of the possible language keywords are available here. However, some keywords may be missing due to variations in the database. If you encounter any missing keywords, you can add them manually.", "You can import these keywords from the translate page of any language as well.": "You can import these keywords from the translate page of any language as well.", "Email Send Method": "Email Send Method", "PHP Mail": "PHP Mail", "SMTP": "SMTP", "SendGrid API": "SendGrid API", "Mailjet API": "Mailjet API", "SMTP Configuration": "SMTP Configuration", "Host": "Host", "smtp.googlemail.com": "smtp.googlemail.com", "Port": "Port", "Available port": "Available port", "Encryption": "Encryption", "SSL": "SSL", "TLS": "TLS", "Normally your email": "Normally your email", "Normally your email password": "Normally your email password", "SendGrid API Configuration": "SendGrid API Configuration", "App Key": "App Key", "SendGrid App key": "SendGrid App key", "Mailjet API Configuration": "Mailjet API Configuration", "Api Public Key": "Api Public Key", "Mailjet Api Public Key": "Mailjet Api Public Key", "Api Secret Key": "Api Secret Key", "Mailjet Api Secret Key": "Mailjet Api Secret Key", "Test Mail Setup": "Test Mail Setup", "Sent to": "Sent to", "Email Address": "Email Address", "Send Test Mail": "Send Test Mail", "Email Sent From - Name": "<PERSON><PERSON> From - Name", "Email address": "Email address", "Email Sent From - Email": "<PERSON><PERSON> From - Email", "Email Body": "Email Body", "Your email template": "Your email template", "Notification Title": "Notification Title", "Push Notification Body": "Push Notification Body", "Short Code": "Short Code", "Description": "Description", "Full Name of User": "Full Name of User", "Username of User": "Username of User", "Message": "Message", "SMS Sent From": "SMS Sent From", "SMS Body": "SMS Body", "Email Template": "<PERSON>ail Te<PERSON>late", "SMS Template": "SMS Template", "Push Notification Template": "Push Notification Template", "If you want to send push notification by the firebase, Your system must be SSL certified": "If you want to send push notification by the firebase, Your system must be SSL certified", "API Key": "API Key", "Auth Domain": "Auth Domain", "Project Id": "Project Id", "Storage Bucket": "Storage Bucket", "Messaging Sender Id": "Messaging Sender Id", "App Id": "App Id", "Measurement Id": "Measurement Id", "Server key": "Server key", "Firebase Setup": "Firebase Setup", "Steps": "Steps", "Configs": "Configs", "Server Key": "Server Key", "To Do": "To Do", "Step 1": "Step 1", "Go to your Firebase account and select": "Go to your Firebase account and select", "Go to console": "Go to console", "in the upper-right corner of the page.": "in the upper-right corner of the page.", "Step 2": "Step 2", "Select Add project and do the following to create your project.": "Select Add project and do the following to create your project.", "Use the name, Enable Google Analytics, Choose a name and the country for Google Analytics, Use the default analytics settings": "Use the name, Enable Google Analytics, Choose a name and the country for Google Analytics, Use the default analytics settings", "Step 3": "Step 3", "Within your Firebase project, select the gear next to Project Overview and choose Project settings.": "Within your Firebase project, select the gear next to Project Overview and choose Project settings.", "Step 4": "Step 4", "Next, set up a web app under the General section of your project settings.": "Next, set up a web app under the General section of your project settings.", "Step 5": "Step 5", "Next, go to Cloud Messaging in your Firebase project settings and enable Cloud Messaging API.": "Next, go to Cloud Messaging in your Firebase project settings and enable Cloud Messaging API.", "Sms Send Method": "Sms Send Method", "Clickatell": "Clickatell", "Infobip": "Infobip", "Message Bird": "Message Bird", "Nexmo": "Nexmo", "Sms Broadcast": "Sms Broadcast", "Twilio": "<PERSON><PERSON><PERSON>", "Text Magic": "Text Magic", "Custom API": "Custom API", "Clickatell Configuration": "Clickatell Configuration", "Infobip Configuration": "Infobip Configuration", "Message Bird Configuration": "Message Bird Configuration", "Nexmo Configuration": "Nexmo Configuration", "API Secret": "API Secret", "Sms Broadcast Configuration": "Sms Broadcast Configuration", "Twilio Configuration": "<PERSON><PERSON><PERSON> Configu<PERSON>", "Account SID": "Account SID", "Auth Token": "<PERSON><PERSON>", "From Number": "From Number", "Text Magic Configuration": "Text Magic Configuration", "Apiv2 Key": "Apiv2 Key", "API URL": "API URL", "GET": "GET", "POST": "POST", "Number": "Number", "Headers": "Headers", "Headers Name": "Headers Name", "Headers Value": "Headers Value", "Body": "Body", "Body Name": "Body Name", "Body Value": "Body Value", "Test SMS Setup": "Test SMS Setup", "Mobile": "Mobile", "Send Test SMS": "Send Test SMS", "Subject": "Subject", "Email subject": "Email subject", "Send Email": "Send Email", "Make the field empty if you want to use global template\\'s name as email sent from name.": "Make the field empty if you want to use global template\\'s name as email sent from name.", "Make the field empty if you want to use global template\\'s email as email sent from.": "Make the field empty if you want to use global template\\'s email as email sent from.", "Your message using short-codes": "Your message using short-codes", "Edit Template": "Edit Template", "SMS": "SMS", "Push": "<PERSON><PERSON>", "Make the field empty if you want to use global template\\'s title as notification title.": "Make the field empty if you want to use global template\\'s title as notification title.", "Send Push Notify": "Send Push Notify", "Make the field empty if you want to use global template\\'s name as sms sent from name.": "Make the field empty if you want to use global template\\'s name as sms sent from name.", "Send SMS": "Send SMS", "Global Template": "Global Template", "Email Setting": "<PERSON>ail <PERSON>ting", "SMS Setting": "SMS Setting", "Push Notification Setting": "Push Notification Setting", "Notification Templates": "Notification Templates", "Are you sure to delete the notification?": "Are you sure to delete the notification?", "Mark All as Read": "<PERSON> as <PERSON>", "Are you sure to delete all notifications?": "Are you sure to delete all notifications?", "Delete all Notification": "Delete all Notification", "Please Set Cron Job": "Please Set <PERSON><PERSON>", "Once per 5-10 minutes is ideal while once every minute is the best option": "Once per 5-10 minutes is ideal while once every minute is the best option", "Cron Command": "Cron Command", "Last Cron Run": "Last Cron Run", "Cron Job Setting": "<PERSON><PERSON>", "Run Manually": "Run Manually", "V": "V", "Search here...": "Search here...", "Update Available": "Update Available", "Visit Website": "Visit Website", "Unread Notifications": "Unread Notifications", "Notification": "Notification", "You have": "You have", "unread notification": "unread notification", "No unread notification found": "No unread notification found", "View all notifications": "View all notifications", "System Setting": "System Setting", "Profile": "Profile", "Logout": "Logout", "Change Password": "Change Password", "Confirm Password": "Confirm Password", "Profile Setting": "Profile Setting", "Profile Information": "Profile Information", "Password Setting": "Password Setting", "Deposit Referral Commission": "Deposit Referral Commission", "Enable Now": "Enable Now", "Disable Now": "Disable Now", "Level": "Level", "Update Setting": "Update Setting", "Number of Level": "Number of Level", "The old setting will be removed after generating new": "The old setting will be removed after generating new", "Login at": "Login at", "IP": "IP", "Location": "Location", "Browser | OS": "Browser | OS", "Lookup IP": "Lookup IP", "Sent": "<PERSON><PERSON>", "Sender": "Sender", "via": "via", "Detail": "Detail", "Notification Details": "Notification Details", "To": "To", "Send Notification": "Send Notification", "Filter": "Filter", "TRX/Username": "TRX/Username", "Plus": "Plus", "Minus": "Minus", "Remark": "Remark", "Start Date - End Date": "Start Date - End Date", "TRX": "TRX", "Transacted": "Transacted", "Post Balance": "Post Balance", "Report & Request": "Report & Request", "Report Bug": "Report Bug", "Feature Request": "Feature Request", "Report a bug": "Report a bug", "Request for Support": "Request for Support", "User Registration": "User Registration", "If you disable this module, no one can register on this system.": "If you disable this module, no one can register on this system.", "Force SSL": "Force SSL", "By enabling": "By enabling", "Force SSL (Secure Sockets Layer)": "Force SSL (Secure Sockets Layer)", "the system will force a visitor that he/she must have to visit in secure mode. Otherwise, the site will be loaded in secure mode.": "the system will force a visitor that he/she must have to visit in secure mode. Otherwise, the site will be loaded in secure mode.", "Agree Policy": "Agree Policy", "If you enable this module, that means a user must have to agree with your system\\'s": "If you enable this module, that means a user must have to agree with your system\\'s", "policies": "policies", "during registration.": "during registration.", "Force Secure Password": "Force Secure Password", "By enabling this module, a user must set a secure password while signing up or changing the password.": "By enabling this module, a user must set a secure password while signing up or changing the password.", "KYC Verification": "KYC Verification", "If you enable": "If you enable", "KYC (Know Your Client)": "KYC (Know Your Client)", "module, users must have to submit": "module, users must have to submit", "the required data": "the required data", "Otherwise, any money out transaction will be prevented by this system.": "Otherwise, any money out transaction will be prevented by this system.", "Email Verification": "Email Verification", "users have to verify their email to access the dashboard. A 6-digit verification code will be sent to their email to be verified.": "users have to verify their email to access the dashboard. A 6-digit verification code will be sent to their email to be verified.", "Note": "Note", "Make sure that the": "Make sure that the", "Email Notification": "Email Notification", "module is enabled": "module is enabled", "If you enable this module, the system will send emails to users where needed. Otherwise, no email will be sent.": "If you enable this module, the system will send emails to users where needed. Otherwise, no email will be sent.", "So be sure before disabling this module that, the system doesn\\'t need to send any emails.": "So be sure before disabling this module that, the system doesn\\'t need to send any emails.", "Mobile Verification": "Mobile Verification", "users have to verify their mobile to access the dashboard. A 6-digit verification code will be sent to their mobile to be verified.": "users have to verify their mobile to access the dashboard. A 6-digit verification code will be sent to their mobile to be verified.", "SMS Notification": "SMS Notification", "If you enable this module, the system will send SMS to users where needed. Otherwise, no SMS will be sent.": "If you enable this module, the system will send SMS to users where needed. Otherwise, no SMS will be sent.", "So be sure before disabling this module that, the system doesn\\'t need to send any SMS.": "So be sure before disabling this module that, the system doesn\\'t need to send any SMS.", "Push Notification": "Push Notification", "If you enable this module, the system will send push notifications to users. Otherwise, no push notification will be sent.": "If you enable this module, the system will send push notifications to users. Otherwise, no push notification will be sent.", "Setting here": "Setting here", "Deposit Referral Bonus": "Deposit Referral Bonus", "If you enable this module, that means users will get the referral commission when his/her child user will make deposits. You can configure the bonus from": "If you enable this module, that means users will get the referral commission when his/her child user will make deposits. You can configure the bonus from", "Referral Setting.": "Referral Setting.", "Register Bonus": "Register Bonus", "If you enable this module, that means users will get a bonus that you\\'ve set up from the": "If you enable this module, that means users will get a bonus that you\\'ve set up from the", "General Setting": "General Setting", "when he/she complete the registration.": "when he/she complete the registration.", "Language Option": "Language Option", "If you enable this module, users can change the language according to their needs.": "If you enable this module, users can change the language according to their needs.", "In App Payment": "In App Payment", "If you enable this module, users can make payment via mobile app using google pay.": "If you enable this module, users can make payment via mobile app using google pay.", "Disabled": "Disabled", "Short Description": "Short Description", "From this page, you can add/update CSS for the user interface. Changing content on this page required programming knowledge.": "From this page, you can add/update CSS for the user interface. Changing content on this page required programming knowledge.", "Please do not change/edit/add anything without having proper knowledge of it. The website may misbehave due to any mistake you have made.": "Please do not change/edit/add anything without having proper knowledge of it. The website may misbehave due to any mistake you have made.", "Write Custom CSS": "Write Custom CSS", "Site Title": "Site Title", "Currency Symbol": "Currency Symbol", "Timezone": "Timezone", "Site Base Color": "Site Base Color", "Site Secondary Color": "Site Secondary Color", "Record to Display Per page": "Record to Display Per page", "20 items per page": "20 items per page", "50 items per page": "50 items per page", "100 items per page": "100 items per page", "Currency Showing Format": "Currency Showing Format", "Show Currency Text and Symbol Both": "Show Currency Text and Symbol Both", "Show Currency Text Only": "Show Currency Text Only", "Show Currency Symbol Only": "Show Currency Symbol Only", "First of all, you have to create an in-app purchase product non-consumable in the Play Store. we assume that you already created some non-consumable products in the Play Store console now we will show the process of how you can set enough necessary processes to verify in-app purchases": "First of all, you have to create an in-app purchase product non-consumable in the Play Store. we assume that you already created some non-consumable products in the Play Store console now we will show the process of how you can set enough necessary processes to verify in-app purchases", "1. Enable APIs in Google Cloud Console": "1. Enable APIs in Google Cloud Console", "Go to": "Go to", "Google Cloud Console": "Google Cloud Console", "and create a new app, or select one": "and create a new app, or select one", "Now go to the": "Now go to the", "Google Play Android Developer API": "Google Play Android Developer API", "page and click on the enable button": "page and click on the enable button", "Go to the": "Go to the", "Google Play Developer Reporting API": "Google Play Developer Reporting API", "2. Create a Service Account in the Google Cloud Console": "2. Create a Service Account in the Google Cloud Console", "Google Cloud console": "Google Cloud console", "IAM & Admin": "IAM & Admin", "Service Accounts": "Service Accounts", "page. Please use the same Google Cloud Project you used in the previous steps. Click the Create Service Account button": "page. Please use the same Google Cloud Project you used in the previous steps. Click the Create Service Account button", "Then a new popup will appear, just enter your service account name then a service account will auto-generate. Just copy the service id(email id) and click the create and continue button": "Then a new popup will appear, just enter your service account name then a service account will auto-generate. Just copy the service id(email id) and click the create and continue button", "Now a new window will be visible, just click on the select a roll drop-down button. Select 2 roles Pub/Sub Admin and Monitoring Viewer. Click on the continue button, and then the done button": "Now a new window will be visible, just click on the select a roll drop-down button. Select 2 roles Pub/Sub Admin and Monitoring Viewer. Click on the continue button, and then the done button", "Find the newly created account in the list and the actions click manage keys. Create a new JSON key and save it locally on your computer. And Upload it to the admin panel": "Find the newly created account in the list and the actions click manage keys. Create a new JSON key and save it locally on your computer. And Upload it to the admin panel", "3. Grant Permissions in the Google Play Console": "3. <PERSON> Permissions in the Google Play Console", "Users and Permissions": "Users and Permissions", "page in the Google Play Console and click Invite new users": "page in the Google Play Console and click Invite new users", "Check on below mentioned permission and click on apply button": "Check on below mentioned permission and click on apply button", "View app information (read only)": "View app information (read only)", "View financial data": "View financial data", "Manage orders subscriptions": "Manage orders subscriptions", "Manage store presence": "Manage store presence", "Note: It takes at least 24 hours for changes to take effect but there is a hacking way. Just check this": "Note: It takes at least 24 hours for changes to take effect but there is a hacking way. Just check this", "link": "link", "Update Google Pay Credential": "Update Google Pay Credential", "File": "File", "Supported Files: .json": "Supported Files: .json", "Update File": "Update File", "Download File": "Download File", "If the logo and favicon are not changed after you update from this page, please": "If the logo and favicon are not changed after you update from this page, please", "clear the cache": "clear the cache", "from your browser. As we keep the filename the same after the update, it may show the old image for the cache. usually, it works after clear the cache but if you still see the old logo or favicon, it may be caused by server level or network level caching. Please clear them too.": "from your browser. As we keep the filename the same after the update, it may show the old image for the cache. usually, it works after clear the cache but if you still see the old logo or favicon, it may be caused by server level or network level caching. Please clear them too.", "Logo": "Logo", "Favicon": "Favicon", "Insert Robots txt": "Insert Robots txt", "Insert Sitemap XML": "Insert Sitemap XML", "Title": "Title", "Client ID": "Client ID", "Enabled": "Enabled", "Are you sure that you want to enable this login credential?": "Are you sure that you want to enable this login credential?", "Are you sure that you want to disable login credential?": "Are you sure that you want to disable login credential?", "Update Credential": "Update Credential", "Client Secret": "Client Secret", "Callback URL": "Callback URL", "How to get": "How to get", "credentials": "credentials", "google developer console": "google developer console", "Click on Select a project than click on": "Click on Select a project than click on", "New Project": "New Project", "and create a project providing the project name": "and create a project providing the project name", "Click on": "Click on", "Click on create credentials and select": "Click on create credentials and select", "OAuth client ID": "OAuth client ID", "Configure Consent Screen": "Configure Consent Screen", "Step 6": "Step 6", "Choose External option and press the create button": "Choose External option and press the create button", "Step 7": "Step 7", "Please fill up the required informations for app configuration": "Please fill up the required informations for app configuration", "Step 8": "Step 8", "Again click on": "Again click on", "and select type as web application and fill up the required informations. Also don\\'t forget to add redirect url and press create button": "and select type as web application and fill up the required informations. Also don\\'t forget to add redirect url and press create button", "Step 9": "Step 9", "Finally you\\'ve got the credentials. Please copy the Client ID and Client Secret and paste it in admin panel google configuration": "Finally you\\'ve got the credentials. Please copy the Client ID and Client Secret and paste it in admin panel google configuration", "facebook developer": "facebook developer", "Click on Get Started and create Meta Developer account": "Click on Get Started and create Meta Developer account", "Create an app by selecting Consumer option": "Create an app by selecting Consumer option", "Click on Setup Facebook Login and select Web option": "Click on Setup Facebook Login and select Web option", "Add site url": "Add site url", "linkedin developer": "linkedin developer", "Click on create app and provide required information": "Click on create app and provide required information", "Click Auth option and copy the credentials and paste it to admin panel and don\\'t forget to add redirect url here": "Click Auth option and copy the credentials and paste it to admin panel and don\\'t forget to add redirect url here", "No search result found.": "No search result found.", "Subscribe At": "Subscribe At", "Are you sure to remove this subscriber?": "Are you sure to remove this subscriber?", "Email will be sent again with a": "Email will be sent again with a", "second delay. Avoid closing or refreshing the browser.": "second delay. Avoid closing or refreshing the browser.", "' . @$sessionData['total_sent'] . ' out of ' . @$sessionData['total_subscriber'] . 'email were successfully transmitted": "' . @$sessionData['total_sent'] . ' out of ' . @$sessionData['total_subscriber'] . 'email were successfully transmitted", "Subject / Title": "Subject / Title", "Start Form": "Start Form", "Start form user id. e.g. 1": "Start form user id. e.g. 1", "Per Batch": "<PERSON>", "How many subscriber": "How many subscriber", "Cooling Period": "Cooling Period", "Waiting time": "Waiting time", "Ticket#": "Ticket#", "Close Ticket": "Close Ticket", "Enter reply here": "Enter reply here", "Add Attachment": "Add Attachment", "Max 5 files can be uploaded | Maximum upload size is '.convertToReadableSize(ini_get('upload_max_filesize": "Max 5 files can be uploaded | Maximum upload size is '.convertToReadableSize(ini_get('upload_max_filesize", "Reply": "Reply", "Are you sure to delete this message?": "Are you sure to delete this message?", "Posted on": "Posted on", "Staff": "Staff", "Close Support Ticket!": "Close Support Ticket!", "Are you want to close this support ticket?": "Are you want to close this support ticket?", "Submitted By": "Submitted By", "Priority": "Priority", "Last Reply": "Last Reply", "Ticket": "Ticket", "Low": "Low", "Medium": "Medium", "High": "High", "Version": "Version", "ViserAdmin Version": "ViserAdmin Version", "Laravel Version": "Laravel Version", "Compiled views will be cleared": "Compiled views will be cleared", "Application cache will be cleared": "Application cache will be cleared", "Route cache will be cleared": "Route cache will be cleared", "Configuration cache will be cleared": "Configuration cache will be cleared", "Compiled services and packages files will be removed": "Compiled services and packages files will be removed", "Caches will be cleared": "Caches will be cleared", "Click to clear": "Click to clear", "PHP Version": "PHP Version", "Server Software": "Server Software", "Server IP Address": "Server IP Address", "Server Protocol": "Server Protocol", "HTTP Host": "HTTP Host", "Server Port": "Server Port", "PHP-zip extension is required to perform the update operation.": "PHP-zip extension is required to perform the update operation.", "The system already customized. You can\\'t update the project.": "The system already customized. You can\\'t update the project.", "Your Version": "Your Version", "Latest Version": "Latest Version", "You are currently using the latest version of the system.": "You are currently using the latest version of the system.", "We are committed to continuous improvement and are actively developing the next version. Stay tuned for exciting new features and enhancements to be released soon!": "We are committed to continuous improvement and are actively developing the next version. Stay tuned for exciting new features and enhancements to be released soon!", "A new system version has already been released that you have not grabbed yet. Don\\'t miss it out. Get the latest features of the system.": "A new system version has already been released that you have not grabbed yet. Don\\'t miss it out. Get the latest features of the system.", "Update the System": "Update the System", "You\\'re about to upgrade the system to the most recent released version": "You\\'re about to upgrade the system to the most recent released version", "The system update is currently underway. Kindly remain on standby as the process nears completion.": "The system update is currently underway. Kindly remain on standby as the process nears completion.", "The system has been successfully updated. It will reload shortly.": "The system has been successfully updated. It will reload shortly.", "Before proceeding, it is strongly advised to create a backup of the system. We highly recommend backing up both your files and database.": "Before proceeding, it is strongly advised to create a backup of the system. We highly recommend backing up both your files and database.", "Don\\'t reload the page or don\\'t go to another page while updating the system.": "Don\\'t reload the page or don\\'t go to another page while updating the system.", "Continue": "Continue", "Update Log": "Update Log", "Uploaded": "Uploaded", "No update log found yet!": "No update log found yet!", "Balance": "Balance", "Logins": "<PERSON><PERSON>", "Notifications": "Notifications", "KYC Data": "KYC Data", "Ban User": "Ban User", "Unban User": "Unban User", "Information of": "Information of", "First Name": "First Name", "Last Name": "Last Name", "Mobile Number": "Mobile Number", "Address": "Address", "City": "City", "State": "State", "Zip/Postal": "Zip/Postal", "Country": "Country", "Unverified": "Unverified", "2FA Verification": "2FA Verification", "KYC": "KYC", "Please provide positive amount": "Please provide positive amount", "If you ban this user he/she won\\'t able to access his/her dashboard.": "If you ban this user he/she won\\'t able to access his/her dashboard.", "Reason": "Reason", "Ban reason was": "Ban reason was", "Are you sure to unban this user?": "Are you sure to unban this user?", "Login as User": "<PERSON><PERSON> as User", "KYC data not found": "KYC data not found", "Rejection Reason": "Rejection Reason", "Are you sure to approve this documents?": "Are you sure to approve this documents?", "Reject KYC Documents": "Reject KYC Documents", "If you reject these documents, the user will be able to re-submit new documents and these documents will be replaced by new documents.": "If you reject these documents, the user will be able to re-submit new documents and these documents will be replaced by new documents.", "Email-Mobile": "Email-Mobile", "Joined At": "Joined At", "' . @$sessionData['total_sent'] . ' out of ' . @$sessionData['total_user'] . ' ' . $viaName . ' were successfully transmitted": "' . @$sessionData['total_sent'] . ' out of ' . @$sessionData['total_user'] . ' ' . $viaName . ' were successfully transmitted", "Send Via Email": "Send Via Email", "Send Via SMS": "Send Via SMS", "Send Via Firebase": "Send Via Firebase", "Being Sent To": "Being Sent To", "active users found to send the notification": "active users found to send the notification", "Image (optional)": "Image (optional)", "Supported Files": "Supported Files", ".png, .jpg, .jpeg": ".png, .jpg, .jpeg", "How many user": "How many user", "Select User": "Select User", "Number Of Top Deposited User": "Number Of Top Deposited User", "Number Of Days": "Number Of Days", "Days": "Days", "Withdraw Instruction": "Withdraw Instruction", "Input Text": "Input Text", "Textarea": "Textarea", "Required": "Required", "Optional": "Optional", "Withdraw Via": "Withdraw Via", "Trx Number": "Trx Number", "Payable": "Payable", "User Withdraw Information": "User Withdraw Information", "Approve Withdrawal Confirmation": "Approve Withdrawal Confirmation", "Have you sent": "Have you sent", "Provide the details. eg: transaction number": "Provide the details. eg: transaction number", "Reject Withdrawal Confirmation": "Reject Withdrawal Confirmation", "Reason of Rejection": "Reason of Rejection", "Withdraw Limit": "Withdraw Limit", "image": "image", "Are you sure to disable this method?": "Are you sure to disable this method?", "Are you sure to enable this method?": "Are you sure to enable this method?", "Approved Withdrawal": "Approved Withdrawal", "Amount after charge": "Amount after charge", "Back": "Back", "Generate Form": "Generate Form", "Text": "Text", "URL": "URL", "Date & Time": "Date & Time", "Time": "Time", "Select": "Select", "Checkbox": "Checkbox", "Radio": "Radio", "Is Required": "Is Required", "Label": "Label", "Width": "<PERSON><PERSON><PERSON>", "100%": "100%", "50%": "50%", "33%": "33%", "25%": "25%", "Instruction": "Instruction", "(if any)": "(if any)", "Supported Files:": "Supported Files:", "Image will be resized into": "Image will be resized into", "px": "px", "Supported mimes": "Supported mimes", "View All": "View All", "Page not found": "Page not found", "page you are looking for doesn\\'t exist or an other error ocurred": "page you are looking for doesn\\'t exist or an other error ocurred", "or temporarily unavailable.": "or temporarily unavailable.", "Go to Home": "Go to Home", "Sorry your session has expired": "Sorry your session has expired", "Please go back and refresh your browser and try again": "Please go back and refresh your browser and try again", "Sorry Internal server error": "Sorry Internal server error", "Something went wrong on our end. We\\'re working on fixing it.": "Something went wrong on our end. We\\'re working on fixing it.", "Captcha": "<PERSON><PERSON>", "Please Allow / Reset Browser Notification": "Please Allow / Reset Browser Notification", "If you want to get push notification then you have to allow notification from your browser": "If you want to get push notification then you have to allow notification from your browser", "Read More": "Read More", "Latest Blog": "Latest Blog", "Most Views": "Most Views", "Write your subject": "Write your subject", "Write your message": "Write your message", "Send Message": "Send Message", "Play Now": "Play Now", "The result is": "The result is", "learn more": "learn more", "Allow": "Allow", "Home": "Home", "Enter email address": "Enter email address", "Subscribe": "Subscribe", "Copyright": "Copyright", "All Rights Reserved": "All Rights Reserved", "Games": "Games", "Blog": "Blog", "Contact": "Contact", "Login": "<PERSON><PERSON>", "Register": "Register", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Deposit Log": "Deposit Log", "Withdraw": "Withdraw", "Withdraw Log": "Withdraw Log", "Referrals": "Referrals", "Reports": "Reports", "Game Log": "Game Log", "Commission Log": "Commission Log", "Transactions": "Transactions", "Support": "Support", "Open New Ticket": "Open New Ticket", "My Tickets": "My Tickets", "Account": "Account", "2FA Security": "2FA Security", "Latest Winners": "Latest Winners", "Latest Transactions": "Latest Transactions", "Transaction ID": "Transaction ID", "User name": "User name", "A 6 digit verification code sent to your email address": "A 6 digit verification code sent to your email address", "If you don\\'t get any code": "If you don\\'t get any code", "try again after": "try again after", "seconds": "seconds", "Try again": "Try again", "A 6 digit verification code sent to your mobile number": "A 6 digit verification code sent to your mobile number", "Username or Email": "Username or Email", "Remember Me": "Remember Me", "Forget Password?": "Forget Password?", "Login Now": "Login Now", "Create an account": "Create an account", "Please check including your Junk/Spam Folder. if not found, you can": "Please check including your Junk/Spam Folder. if not found, you can", "To recover your account please provide your email or username to find your account.": "To recover your account please provide your email or username to find your account.", "Email or Username": "Email or Username", "Your account is verified successfully. Now you can change your password. Please enter a strong password and don\\'t share it with anyone.": "Your account is verified successfully. Now you can change your password. Please enter a strong password and don\\'t share it with anyone.", "Reference By": "Reference By", "I agree with": "I agree with", "Already i have an account in here": "Already i have an account in here", "You are with us": "You are with us", "You already have an account please Login": "You already have an account please Login", "Commission From": "Commission From", "Commission Level": "Commission Level", "Transaction": "Transaction", "KYC Documents Rejected": "KYC Documents Rejected", "Show Reason": "Show Reason", "Click Here to Re-submit Documents": "Click Here to Re-submit Documents", "See KYC Data": "See KYC Data", "KYC Verification required": "KYC Verification required", "Click Here to Submit Documents": "Click Here to Submit Documents", "KYC Verification pending": "KYC Verification pending", "Total Balance": "Total Balance", "Total Deposit": "Total Deposit", "Total Withdraw": "Total Withdraw", "Total Invest": "Total Invest", "Total Win": "Total Win", "Total Loss": "Total Loss", "KYC Document Rejection Reason": "KYC Document Rejection Reason", "Search by transactions": "Search by transactions", "Processing Charge": "Processing Charge", "Automatically processed": "Automatically processed", "Admin Feedback": "<PERSON><PERSON>", "Current Balance :": "Current Balance :", "Enter amount": "Enter amount", "Minimum": "Minimum", "Maximum": "Maximum", "Dealer": "Dealer", "Dealer Score": "Dealer Score", "Player": "Player", "You": "You", "Your Score": "Your Score", "Hit": "Hit", "Stay": "Stay", "Play Again": "Play Again", "Game Rule": "Game Rule", "Current Balance": "Current Balance", "Enter Amount": "Enter Amount", "min": "min", "max": "max", "Bonus": "Bonus", "Random": "Random", "Refresh": "Refresh", "Minimum :": "Minimum :", "Maximum :": "Maximum :", "How To Win": "How To Win", "Click the": "Click the", "number that are on your scratch off, and then click": "number that are on your scratch off, and then click", "\"play Now\"": "\"play Now\"", "button to see if you are a winner!": "button to see if you are a winner!", "If match": "If match", "number": "number", "Bet Amount": "Bet Amount", "Min": "Min", "Max": "Max", "Start Game": "Start Game", "Cashout": "Cashout", "You Will Get": "You Will Get", "Chances Per Invest": "Chances Per Invest", "Win Bonus For This Chance": "Win Bonus For This Chance", "Guess The Number": "Guess The Number", "Single": "Single", "Double": "Double", "Triple": "Triple", "Enter Number": "Enter Number", "DEAL": "DEAL", "CALL": "CALL", "FOLD": "FOLD", "Invest Limit": "Invest Limit", "VS": "VS", "Maximum:": "Maximum:", "Your Current Balance": "Your Current Balance", "Limit": "Limit", "Even": "Even", "Red": "Red", "Black": "Black", "Odd": "Odd", "Get Bonus": "Get Bonus", "You Select": "You Select", "Win or Lost": "Win or Lost", "Lost": "Lost", "KYC Form": "KYC Form", "Current Password": "Current Password", "Authorize Net": "Authorize Net", "Name on Card": "Name on Card", "Card Number": "Card Number", "Expiration Date": "Expiration Date", "CVC Code": "CVC Code", "Checkout.com": "Checkout.com", "Payment Preview": "Payment Preview", "PLEASE SEND EXACTLY": "PLEASE SEND EXACTLY", "TO": "TO", "SCAN TO SEND": "SCAN TO SEND", "payment-thumb": "payment-thumb", "Show All Payment Options": "Show All Payment Options", "00.00": "00.00", "0.00": "0.00", "Processing charge for payment gateways": "Processing charge for payment gateways", "Total": "Total", "In": "In", "Conversion with": "Conversion with", "and final value will Show on next step": "and final value will Show on next step", "Deposit Confirm": "Deposit Confirm", "Ensuring your funds grow safely through our secure deposit process with world-class payment options.": "Ensuring your funds grow safely through our secure deposit process with world-class payment options.", "Flutterwave": "Flutterwave", "You have to pay": "You have to pay", "You will get": "You will get", "Pay Now": "Pay Now", "You are requesting": "You are requesting", "to deposit.": "to deposit.", "Please pay": "Please pay", "for successful payment.": "for successful payment.", "NMI": "NMI", "Paystack": "Paystack", "Razorpay": "Razorpay", "Stripe Hosted": "Stripe Hosted", "Stripe Storefront": "Stripe Storefront", "Deposit with Stripe": "Deposit with <PERSON><PERSON>", "E-mail Address": "E-mail Address", "Zip Code": "Zip Code", "You are referred by": "You are referred by", "Max 5 files can be uploaded | Maximum upload size is ' . convertToReadableSize(ini_get('upload_max_filesize": "Max 5 files can be uploaded | Maximum upload size is ' . convertToReadableSize(ini_get('upload_max_filesize", "Are you sure to close this ticket?": "Are you sure to close this ticket?", "No replies found here!": "No replies found here!", "Trx": "Trx", "Add Your Account": "Add Your Account", "Use the QR code or setup key on your Google Authenticator app to add your account.": "Use the QR code or setup key on your Google Authenticator app to add your account.", "Setup Key": "Setup Key", "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.": "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.", "Download": "Download", "Disable 2FA Security": "Disable 2FA Security", "Google Authenticator OTP": "Google Authenticator OTP", "Enable 2FA Security": "Enable 2FA Security", "Processing charge for withdraw method": "Processing charge for withdraw method", "Receivable": "Receivable", "Confirm Withdraw": "Confirm Withdraw", "Safely withdraw your funds using our highly secure process and various withdrawal method": "Safely withdraw your funds using our highly secure process and various withdrawal method", "for withdraw.": "for withdraw.", "The admin will send you": "The admin will send you", "to your account.": "to your account.", "Google Authenticator Code": "Google Authenticator Code", "Share This": "Share This", "Our Address": "Our Address", "Call Us": "Call Us", "Mail Us": "Mail Us", "Full Name": "Full Name", "Readmore": "Readmore", "All rights reserved": "All rights reserved", "Game": "Game", "Deposit Now": "Deposit Now", "Withdraw Now": "Withdraw Now", "Latest Winner": "Latest Winner", "Data": "Data", "Haven\\'t an Account?": "Haven\\'t an Account?", "Create Account": "Create Account", "Already have an account?": "Already have an account?", "Click Here to Verify": "Click Here to Verify", "How To Win ?": "How To Win ?", "Previous Replies": "Previous Replies", "Any": "Any", "Google Authenticatior OTP": "Google Authenticatior OTP", "We may use cookies or any other tracking technologies when you visit our website, including any other media form, mobile website, or mobile application related or connected to help customize the Site and improve your experience.": "We may use cookies or any other tracking technologies when you visit our website, including any other media form, mobile website, or mobile application related or connected to help customize the Site and improve your experience.", "0": "1", "Privacy Policy": "Privacy Policy", "Terms of Service": "Terms of Service", "THE SITE IS UNDER MAINTENANCE": "THE SITE IS UNDER MAINTENANCE", "Play online games and win a lot of bonuses": "Play online games and win a lot of bonuses", "Lorem ipsum dolor sit amet consectetur adipisicing elit. Quos error quo cum illum, alias similique, suscipit nihil tempore.": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Quos error quo cum illum, alias simi<PERSON>, suscipit nihil tempore.", "Sign Up": "Sign Up", "user/register": "user/register", "Sign In": "Sign In", "user/login": "user/login", "About": "About", "Maecenas sagittis turpis vel orci malesuada": "Mae<PERSON>nas sagittis turpis vel orci malesuada", "Learn More": "Learn More", "about-us": "about-us", "Best Platform": "Best Platform", "Quick Deposit": "Quick Deposit", "Quick Withdraw": "Quick Withdraw", "24/7 Support": "24/7 Support", "Our Awesome Games": "Our Awesome Games", "Dolor sit amet consectetur adipisicing elit. Ipsa, harum quidem fuga ipsam dolores odio architecto, non neque minima atque nisi temporibus ullam": "Dolor sit amet consectetur adipisicing elit. <PERSON><PERSON><PERSON>, harum quidem fuga ipsam dolores odio architecto, non neque minima atque nisi temporibus ullam", "Latest Transactions And Winners": "Latest Transactions And Winners", "Why Choose Xaxino": "<PERSON> <PERSON><PERSON>", "Dolor sit amet consectetur adipisicing elit. Ipsa, harum quidem fuga ipsam dolores odio architecto, non neque minima atque nisi tempor": "Dolor sit amet consectetur adipisicing elit. <PERSON><PERSON><PERSON>, harum quidem fuga ipsam dolores odio architecto, non neque minima atque nisi tempor", "Awesome Gaming Platform": "Awesome Gaming Platform", "Adipisci harum cum, ipsum nulla hic earum quidem repellat ad! At quam odio non harum minima nihil exercitationem ex, distinctio.": "Adipisci harum cum, ipsum nulla hic earum quidem repellat ad! At quam odio non harum minima nihil exercitationem ex, distinctio.", "Referral Commission System": "Referral Commission System", "Secure Betting Platform": "Secure Betting Platform", "Invest Win And Earn": "Invest Win And Earn", "Quick Response": "Quick Response", "26+ Payment Gateway": "26+ Payment Gateway", "Total User": "Total User", "1,255,000": "1,255,000", "Total Winners": "Total Winners", "845,000": "845,000", "4,845,000": "4,845,000", "945,000": "945,000", "Frequently Asked Questions": "Frequently Asked Questions", "Why Xaxino?": "Why <PERSON><PERSON><PERSON>?", "Donec quisque sem molestie tortor ut, libero libero interdum nec quisque, et scelerisque nam, elit lectus mauris sed maecenas. Veniam urna eget habitasse aliquam": "Donec quisque sem molestie tortor ut, libero libero interdum nec quisque, et scelerisque nam, elit lectus mauris sed maecenas. Veniam urna eget habitasse aliquam", "How to prediction?": "How to prediction?", "Our vission And mission?": "Our vission And mission?", "How to predict?": "How to predict?", "How Work Xaxino": "How <PERSON> Xaxino", "Win Lottery!": "Win Lottery!", "Amet odit iure eaeos autiste perferendis numquam sint excepturi.": "Amet odit iure eaeos autiste perferendis numquam sint excepturi.", "Confirm Lottery": "Confirm Lottery", "Pick Number": "Pick Number", "Choose Lottery": "<PERSON><PERSON>", "Buy ticket and get million dollars for a click": "Buy ticket and get million dollars for a click", "games": "games", "15% Referral Commission": "15% Referral Commission", "What User Say About Xaxino": "What User Say About Xaxino", "Auyesha Hatun": "<PERSON><PERSON><PERSON>", "Doloribus porro nobis in provident rem eum reandae quasi voluptatum, quibusdam et itaque tenetur quos alias quo harum officiis quis vero. Enim omnis porro, cupiditate repellat harum et eius distinctio neque dolorem expedita obcaecati commodi.": "Doloribus porro nobis in provident rem eum reandae quasi voluptatum, quibusdam et itaque tenetur quos alias quo harum officiis quis vero. Enim omnis porro, cupiditate repellat harum et eius distinctio neque dolorem expedita obcaecati commodi.", "Raba Khan": "<PERSON><PERSON>", "Shunil Bhat": "<PERSON><PERSON><PERSON>", "Raziya Khanam": "<PERSON><PERSON><PERSON>", "We accept 21+ payment methods": "We accept 21+ payment methods", "Our Blog News": "Our Blog News", "Aut modi soluta nihil, repellat adipisci similique dolores.": "Aut modi soluta nihil, repellat adipisci similique dolores.", "Delectus velit adipisci amet offici molestias minus qui praesentium itaque incidunt sunt porro maxime sit veniam facere, reprehen.": "Delectus velit adipisci amet offici molestias minus qui praesentium itaque incidunt sunt porro maxime sit veniam facere, reprehen.", "Welcome to Xaxino": "Welcome to Xaxino", "Sit iste delectus iure animi facere. Est veritatis illo officia.": "Sit iste delectus iure animi facere. Est veritatis illo officia.", "Quick Support": "Quick Support", "You can get all information": "You can get all information", "Get in touch": "Get in touch", "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sunt ea possimus facilis aut veritatis, voluptate ullam, dolorem fugiat maxime cupiditate reiciendis voluptatum  incidunt deserunt.": "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Sunt ea possimus facilis aut veritatis, voluptate ullam, dolorem fugiat maxime cupiditate reiciendis voluptatum  incidunt deserunt.", "Subscribe to get updates": "Subscribe to get updates", "Lorem ipsum dolor sit amet soluta consectetur adipisicing elit. Iste amet soluta possimus veniam non eaque.": "Lorem ipsum dolor sit amet soluta consectetur adipisicing elit. Iste amet soluta possimus veniam non eaque.", "Facebook": "Facebook", "https://www.facebook.com/": "https://www.facebook.com/", "Linkedin": "Linkedin", "https://www.google.com/": "https://www.google.com/", "Twitter": "Twitter", "https://www.twitter.com/": "https://www.twitter.com/", "Instagram": "Instagram", "https://www.instagram.com/": "https://www.instagram.com/", "Lorem ipsum, dolor sit amet consectetur adipisicing elit. Hic officia quod natus, non dicta perspiciatis, quae repellendus ea illum aut debitis sint amet? Ratione voluptates beatae numquam.": "<PERSON>rem ipsum, dolor sit amet consectetur adipisicing elit. Hic officia quod natus, non dicta perspiciatis, quae repellendus ea illum aut debitis sint amet? Ratione voluptates beatae numquam.", "Lorem ipsum, dolor sit amet consectetur adipisicing elit. Hic officia quod natus, non dicta perspiciatis, quae repellendus ea illum aut debitis sint amet .": "<PERSON>rem ipsum, dolor sit amet consectetur adipisicing elit. Hic officia quod natus, non dicta perspiciatis, quae repellendus ea illum aut debitis sint amet .", "+7556555555555": "+7556555555555", "<EMAIL>": "<EMAIL>", "Visit Us": "Visit Us", "Velit minus rerum si": "Velit minus rerum si", "You Are Banned": "You Are Banned", "Lucky Dwarfs": "<PERSON>", "Get insane prizes every week": "Get insane prizes every week", "Dolor sit amet consectetur adipisicing elit. Ipsaharum quidem fuga ipsam dolores odio architecto": "Dolor sit amet consectetur adipisicing elit. Ipsaharum quidem fuga ipsam dolores odio architecto", "Get 15% Referral Commission": "Get 15% Referral Commission", "Referral commissions are also known as a finders fee and referral rewards. Learn how to structure them": "Referral commissions are also known as a finders fee and referral rewards. Learn how to structure them", "Know More": "Know More", "/user/login": "/user/login", "Why You Should Choose XAX": "Why You Should Choose XAX", "INO": "INO", "1,25,000": "1,25,000", "Total Winner": "Total Winner", "2,25,000": "2,25,000", "Adipisci harum cumipsum nulla hic earum quidem repellat ad! At quam odio non harum": "Adipisci harum cumipsum nulla hic earum quidem repellat ad! At quam odio non harum", "Awsome Gaming Platform": "Awsome Gaming Platform", "Adipisci harum cumipsum nulla hic earum quidem repellat ad!": "Adipisci harum cumipsum nulla hic earum quidem repellat ad!", "At quam odio non harum": "At quam odio non harum", "Adipisci harum cumipsum nulla hic earum quidem repellat ad!    At quam odio non harum": "Adipisci harum cumipsum nulla hic earum quidem repellat ad!    At quam odio non harum", "Latest Transactions & Winners": "Latest Transactions & Winners", "Know About XAXINO": "Know About XAXINO", "Casinos offer a wide variety of games, each with its own set of rules, strategies, and odds. Here are brief descriptions of some popular casino games:": "Casinos offer a wide variety of games, each with its own set of rules, strategies, and odds. Here are brief descriptions of some popular casino games:", "How to Win Casion Game": "How to Win Casion Game", "Sign Up Account": "Sign Up Account", "Win Lottery": "Win Lottery", "Asked Your Question About XAX": "Asked Your Question About XAX", "Quisque sem molestie tortor ut, libero libero interdum nec quisque, et scelerisque nam, elit lectus mauris sed maecenas. Veniam urna eget habitasse aliquam": "Quisque sem molestie tortor ut, libero libero interdum nec quisque, et scelerisque nam, elit lectus mauris sed maecenas. Veniam urna eget habitasse aliquam", "Our Mission and Vission": "Our Mission and Vission", "How to Prediction?": "How to Prediction?", "How to Play?": "How to Play?", "What User Say About XAX": "What User Say About XAX", "/games": "/games", "Aymond": "<PERSON><PERSON><PERSON>", "Vivamus in erat ut urna cursus vestibulum. Sed hendrerit. Nunc egestas, augue at pellentesque laoreet, felis eros vehicula leo.": "<PERSON>mus in erat ut urna cursus vestibulum. Sed hendrerit. Nunc egestas, augue at pellentesque laoreet, felis eros vehicula leo.", "Daymon": "<PERSON><PERSON>", "Alicent": "<PERSON><PERSON>", "We accept 25+ payment methods": "We accept 25+ payment methods", "https://www.facebook.com": "https://www.facebook.com", "https://www.linkedin.com/": "https://www.linkedin.com/", "4517 Washington Ave.Manchester, Kentucky 39495": "4517 Washington Ave.Manchester, Kentucky 39495", "(408) 777 - 8745": "(408) 777 - 8745", "Phasellus a est. Vivamus elementum semper nisi. In turpis. Proin sapien ipsum, porta a, auctor quis, euismod ut, mi. Praesent nec nisl a purus blandit viverra. Phasellus leo dolor, tempus non, auctor et, hendrerit quis, nisi. Aenean posuere, tortor sed cursus feugiat, nunc augue blandit nunc, eu sollicitudin urna dolor sagittis lacus.": "Phasellus a est. Vivamus elementum semper nisi. In turpis. Proin sapien ipsum, porta a, auctor quis, euismod ut, mi. Praesent nec nisl a purus blandit viverra. Phasellus leo dolor, tempus non, auctor et, hendrerit quis, nisi. <PERSON><PERSON><PERSON> posuere, tortor sed cursus feugiat, nunc augue blandit nunc, eu sollicitudin urna dolor sagittis lacus.", "Pellentesque egestas, neque sit amet convallis pulvinar, justo nulla eleifend augue, ac auctor orci leo non est. Fusce fermentum odio nec arcu. Ut id nisl quis enim dignissim sagittis. Vivamus elementum semper nisi. Sed magna purus, fermentum eu, tincidunt eu, varius ut, felis.": "Pellentesque egestas, neque sit amet convallis pulvinar, justo nulla eleifend augue, ac auctor orci leo non est. Fusce fermentum odio nec arcu. Ut id nisl quis enim dignissim sagittis. Vivamus elementum semper nisi. Sed magna purus, fermentum eu, tincidunt eu, varius ut, felis.", "Sint sit dolore dic": "Sint sit dolore dic", "Aenean posuere, tortor sed cursus feugiat": "<PERSON><PERSON><PERSON> p<PERSON>, tortor sed cursus feugiat", "Morbi nec metus. Fusce convallis metus": "Morbi nec metus. Fusce convallis metus", "Our Latest Update": "Our Latest Update", "Suspendisse enim turpis, dictum sed, iaculis a": "Suspendisse enim turpis, dictum sed, iaculis a", "Donec sodales sagittis magna. In dui magna": "Donec sodales sagittis magna. In dui magna", "Maecenas vestibulum mollis diam": "Maecenas vestibulum mollis diam", "Etiam rhoncus. Sed a libero. Sed hendrerit.": "Etiam rhoncus. Sed a libero. Sed hendrerit.", "Etiam ultricies nisi vel augue. Pellentesque commodo eros a enim.": "Etiam ultricies nisi vel augue. Pellentesque commodo eros a enim.", "Registration Disabled": "Registration Disabled", "Stay Tuned for Updates": "Stay Tuned for Updates", "Go Home": "Go Home", "/": "/"}