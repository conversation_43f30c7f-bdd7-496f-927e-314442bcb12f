<div class="headtail-body__shape">
    <svg width="100%" height="100%" viewBox="0 0 672 1013" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g style="mix-blend-mode:screen" opacity="0.71">
            <path
                d="M4.42347 168.341L71.0297 42.541H195.026L214.822 79.9919H641.84L664.397 122.596V435.839L642.173 477.878V690.645L664.763 733.311V888.079L643.204 928.861H68.5012L7.98334 814.497V486.8L24.7513 455.131V289.366L3.625 249.464L4.42347 168.341Z"
                fill="hsl(var(--black))"></path>
        </g>
        <path
            d="M643.769 931.437H67.9347L67.5355 930.683L6.5852 815.628V485.732L23.2201 454.314V290.937L2.09375 251.035L2.9255 167.776L3.32474 167.022L70.4633 40.1531H195.591L215.553 77.8555H642.471L665.76 121.842V437.348L643.536 479.323V689.954L666.126 732.62V889.713L643.769 931.437ZM69.0992 926.159H642.471L663.232 886.948V734.442L640.642 691.713V476.747L662.866 434.771V123.664L641.141 82.6312H214.255L194.46 44.9288H71.6277L5.81998 169.472L5.05479 248.396L26.1478 288.297V456.199L9.51293 487.932V813.429L69.0992 926.159Z"
            fill="url(#paint0_linear_108_231)"></path>
        <path d="M125.927 940.674H65.908L1.73047 819.398V486.8H14.2399V809.596L71.0981 916.985H125.927V940.674Z"
            fill="url(#paint1_linear_108_231)"></path>
        <path
            d="M647.631 937.218L638.781 920.503L658.51 883.178V738.212L637.75 699.002L646.6 682.287L671.02 728.41V892.981L647.631 937.218Z"
            fill="url(#paint2_linear_108_231)"></path>
        <path d="M670.654 435.84H658.144V127.497L639.247 91.8054H607.707V68.1785H644.437L670.654 117.694V435.84Z"
            fill="url(#paint3_linear_108_231)"></path>
        <path
            d="M8.88304 176.699L0 159.984L68.436 30.7275H197.623L217.418 68.1785H265.36V91.8054H212.228L192.433 54.3544H73.6261L8.88304 176.699Z"
            fill="url(#paint4_linear_108_231)"></path>
        <path
            d="M429.181 56.8678H348.569L333.963 29.2822H225.703V26.6431H334.529L349.167 54.2286H428.616L444.951 23.3755H509.328L521.672 0H641.809V2.63925H522.237L509.894 26.0147H445.517L429.181 56.8678Z"
            fill="url(#paint5_linear_108_231)"></path>
        <path d="M531.587 19.2911L521.34 38.5822H535.812L546.026 19.2911H531.587Z" fill="url(#paint6_linear_108_231)">
        </path>
        <path d="M560.497 19.2911L550.25 38.5822H564.722L574.936 19.2911H560.497Z" fill="url(#paint7_linear_108_231)">
        </path>
        <path d="M618.319 19.2911L608.072 38.5822H622.545L632.758 19.2911H618.319Z" fill="url(#paint8_linear_108_231)">
        </path>
        <path d="M589.407 19.2911L579.16 38.5822H593.633L603.846 19.2911H589.407Z" fill="url(#paint9_linear_108_231)">
        </path>
        <path d="M669.853 497.671L659.605 478.38V505.652L669.853 524.943V497.671Z" fill="url(#paint10_linear_108_231)">
        </path>
        <path d="M669.853 552.277L659.605 532.923V560.257L669.853 579.548V552.277Z" fill="url(#paint11_linear_108_231)">
        </path>
        <path d="M669.853 661.488L659.605 642.134V669.468L669.853 688.759V661.488Z" fill="url(#paint12_linear_108_231)">
        </path>
        <path d="M669.853 606.882L659.605 587.529V614.863L669.853 634.154V606.882Z" fill="url(#paint13_linear_108_231)">
        </path>
        <path d="M2.36133 428.173L12.5419 447.653L12.6417 420.319L2.49441 400.839L2.36133 428.173Z"
            fill="url(#paint14_linear_108_231)"></path>
        <path d="M2.79297 318.962L12.9403 338.442L13.0401 311.107L2.89277 291.628L2.79297 318.962Z"
            fill="url(#paint15_linear_108_231)"></path>
        <path d="M2.59375 373.568L12.7411 393.047L12.8409 365.713L2.69356 346.233L2.59375 373.568Z"
            fill="url(#paint16_linear_108_231)"></path>
        <path d="M508.895 41.0956H226.4V43.7349H508.895V41.0956Z" fill="url(#paint17_linear_108_231)"></path>
        <path
            d="M431.808 1013H274.109L273.676 1012.18L243.833 955.818H172.137L158.529 930.055L160.492 926.347L173.301 950.54H244.964L245.397 951.294L275.24 1007.72H430.643L460.919 950.54H532.582L545.391 926.347L547.387 930.055L533.746 955.818H462.083L431.808 1013Z"
            fill="url(#paint18_linear_108_231)"></path>
        <path d="M304.817 961.839L294.57 981.13H309.043L319.257 961.839H304.817Z" fill="url(#paint19_linear_108_231)">
        </path>
        <path d="M333.728 961.839L323.48 981.13H337.953L348.167 961.839H333.728Z" fill="url(#paint20_linear_108_231)">
        </path>
        <path d="M391.552 961.839L381.305 981.13H395.777L405.991 961.839H391.552Z" fill="url(#paint21_linear_108_231)">
        </path>
        <path d="M362.64 961.839L352.393 981.13H366.865L377.079 961.839H362.64Z" fill="url(#paint22_linear_108_231)">
        </path>
        <defs>
            <linearGradient id="paint0_linear_108_231" x1="334.11" y1="40.1531" x2="334.11" y2="931.437"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint1_linear_108_231" x1="63.8286" y1="486.8" x2="63.8286" y2="940.674"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint2_linear_108_231" x1="654.385" y1="682.287" x2="654.385" y2="937.218"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint3_linear_108_231" x1="639.18" y1="68.1785" x2="639.18" y2="435.84"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint4_linear_108_231" x1="132.68" y1="30.7275" x2="132.68" y2="176.699"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint5_linear_108_231" x1="433.756" y1="0" x2="433.756" y2="56.8678"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint6_linear_108_231" x1="533.683" y1="19.2911" x2="533.683" y2="38.5822"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint7_linear_108_231" x1="562.593" y1="19.2911" x2="562.593" y2="38.5822"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint8_linear_108_231" x1="620.415" y1="19.2911" x2="620.415" y2="38.5822"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint9_linear_108_231" x1="591.503" y1="19.2911" x2="591.503" y2="38.5822"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint10_linear_108_231" x1="664.729" y1="478.38" x2="664.729" y2="524.943"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint11_linear_108_231" x1="664.729" y1="532.923" x2="664.729" y2="579.548"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint12_linear_108_231" x1="664.729" y1="642.134" x2="664.729" y2="688.759"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint13_linear_108_231" x1="664.729" y1="587.529" x2="664.729" y2="634.154"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint14_linear_108_231" x1="7.50152" y1="400.839" x2="7.50152" y2="447.653"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint15_linear_108_231" x1="7.91651" y1="291.628" x2="7.91651" y2="338.442"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint16_linear_108_231" x1="7.71731" y1="346.233" x2="7.71731" y2="393.047"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint17_linear_108_231" x1="367.647" y1="41.0956" x2="367.647" y2="43.7349"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint18_linear_108_231" x1="352.958" y1="926.347" x2="352.958" y2="1013"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint19_linear_108_231" x1="306.913" y1="961.839" x2="306.913" y2="981.13"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint20_linear_108_231" x1="335.824" y1="961.839" x2="335.824" y2="981.13"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint21_linear_108_231" x1="393.648" y1="961.839" x2="393.648" y2="981.13"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
            <linearGradient id="paint22_linear_108_231" x1="364.736" y1="961.839" x2="364.736" y2="981.13"
                gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))"></stop>
                <stop offset="1" stop-color="hsl(var(--base))"></stop>
            </linearGradient>
        </defs>
    </svg>
</div>
