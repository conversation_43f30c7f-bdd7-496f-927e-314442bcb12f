{"AF": {"country": "Afghanistan", "dial_code": "93"}, "AX": {"country": "Aland Islands", "dial_code": "358"}, "AL": {"country": "Albania", "dial_code": "355"}, "DZ": {"country": "Algeria", "dial_code": "213"}, "AS": {"country": "AmericanSamoa", "dial_code": "1684"}, "AD": {"country": "Andorra", "dial_code": "376"}, "AO": {"country": "Angola", "dial_code": "244"}, "AI": {"country": "<PERSON><PERSON><PERSON>", "dial_code": "1264"}, "AQ": {"country": "Antarctica", "dial_code": "672"}, "AG": {"country": "Antigua and Barbuda", "dial_code": "1268"}, "AR": {"country": "Argentina", "dial_code": "54"}, "AM": {"country": "Armenia", "dial_code": "374"}, "AW": {"country": "Aruba", "dial_code": "297"}, "AU": {"country": "Australia", "dial_code": "61"}, "AT": {"country": "Austria", "dial_code": "43"}, "AZ": {"country": "Azerbaijan", "dial_code": "994"}, "BS": {"country": "Bahamas", "dial_code": "1242"}, "BH": {"country": "Bahrain", "dial_code": "973"}, "BD": {"country": "Bangladesh", "dial_code": "880"}, "BB": {"country": "Barbados", "dial_code": "1246"}, "BY": {"country": "Belarus", "dial_code": "375"}, "BE": {"country": "Belgium", "dial_code": "32"}, "BZ": {"country": "Belize", "dial_code": "501"}, "BJ": {"country": "Benin", "dial_code": "229"}, "BM": {"country": "Bermuda", "dial_code": "1441"}, "BT": {"country": "Bhutan", "dial_code": "975"}, "BO": {"country": "Plurinational State of Bolivia", "dial_code": "591"}, "BA": {"country": "Bosnia and Herzegovina", "dial_code": "387"}, "BW": {"country": "Botswana", "dial_code": "267"}, "BR": {"country": "Brazil", "dial_code": "55"}, "IO": {"country": "British Indian Ocean Territory", "dial_code": "246"}, "BN": {"country": "Brunei Darussalam", "dial_code": "673"}, "BG": {"country": "Bulgaria", "dial_code": "359"}, "BF": {"country": "Burkina Faso", "dial_code": "226"}, "BI": {"country": "Burundi", "dial_code": "257"}, "KH": {"country": "Cambodia", "dial_code": "855"}, "CM": {"country": "Cameroon", "dial_code": "237"}, "CA": {"country": "Canada", "dial_code": "1"}, "CV": {"country": "Cape Verde", "dial_code": "238"}, "KY": {"country": "Cayman Islands", "dial_code": " 345"}, "CF": {"country": "Central African Republic", "dial_code": "236"}, "TD": {"country": "Chad", "dial_code": "235"}, "CL": {"country": "Chile", "dial_code": "56"}, "CN": {"country": "China", "dial_code": "86"}, "CX": {"country": "Christmas Island", "dial_code": "61"}, "CC": {"country": "Cocos (Keeling) Islands", "dial_code": "61"}, "CO": {"country": "Colombia", "dial_code": "57"}, "KM": {"country": "Comoros", "dial_code": "269"}, "CG": {"country": "Congo", "dial_code": "242"}, "CD": {"country": "The Democratic Republic of the Congo", "dial_code": "243"}, "CK": {"country": "Cook Islands", "dial_code": "682"}, "CR": {"country": "Costa Rica", "dial_code": "506"}, "CI": {"country": "Cote d'Ivoire", "dial_code": "225"}, "HR": {"country": "Croatia", "dial_code": "385"}, "CU": {"country": "Cuba", "dial_code": "53"}, "CY": {"country": "Cyprus", "dial_code": "357"}, "CZ": {"country": "Czech Republic", "dial_code": "420"}, "DK": {"country": "Denmark", "dial_code": "45"}, "DJ": {"country": "Djibouti", "dial_code": "253"}, "DM": {"country": "Dominica", "dial_code": "1767"}, "DO": {"country": "Dominican Republic", "dial_code": "1849"}, "EC": {"country": "Ecuador", "dial_code": "593"}, "EG": {"country": "Egypt", "dial_code": "20"}, "SV": {"country": "El Salvador", "dial_code": "503"}, "GQ": {"country": "Equatorial Guinea", "dial_code": "240"}, "ER": {"country": "Eritrea", "dial_code": "291"}, "EE": {"country": "Estonia", "dial_code": "372"}, "ET": {"country": "Ethiopia", "dial_code": "251"}, "FK": {"country": "Falkland Islands (Malvinas)", "dial_code": "500"}, "FO": {"country": "Faroe Islands", "dial_code": "298"}, "FJ": {"country": "Fiji", "dial_code": "679"}, "FI": {"country": "Finland", "dial_code": "358"}, "FR": {"country": "France", "dial_code": "33"}, "GF": {"country": "French Guiana", "dial_code": "594"}, "PF": {"country": "French Polynesia", "dial_code": "689"}, "GA": {"country": "Gabon", "dial_code": "241"}, "GM": {"country": "Gambia", "dial_code": "220"}, "GE": {"country": "Georgia", "dial_code": "995"}, "DE": {"country": "Germany", "dial_code": "49"}, "GH": {"country": "Ghana", "dial_code": "233"}, "GI": {"country": "Gibraltar", "dial_code": "350"}, "GR": {"country": "Greece", "dial_code": "30"}, "GL": {"country": "Greenland", "dial_code": "299"}, "GD": {"country": "Grenada", "dial_code": "1473"}, "GP": {"country": "Guadeloupe", "dial_code": "590"}, "GU": {"country": "Guam", "dial_code": "1671"}, "GT": {"country": "Guatemala", "dial_code": "502"}, "GG": {"country": "Guernsey", "dial_code": "44"}, "GN": {"country": "Guinea", "dial_code": "224"}, "GW": {"country": "Guinea-Bissau", "dial_code": "245"}, "GY": {"country": "Guyana", "dial_code": "595"}, "HT": {"country": "Haiti", "dial_code": "509"}, "VA": {"country": "Holy See (Vatican City State)", "dial_code": "379"}, "HN": {"country": "Honduras", "dial_code": "504"}, "HK": {"country": "Hong Kong", "dial_code": "852"}, "HU": {"country": "Hungary", "dial_code": "36"}, "IS": {"country": "Iceland", "dial_code": "354"}, "IN": {"country": "India", "dial_code": "91"}, "ID": {"country": "Indonesia", "dial_code": "62"}, "IR": {"country": "Iran - Islamic Republic of Persian Gulf", "dial_code": "98"}, "IQ": {"country": "Iraq", "dial_code": "964"}, "IE": {"country": "Ireland", "dial_code": "353"}, "IM": {"country": "Isle of Man", "dial_code": "44"}, "IL": {"country": "Israel", "dial_code": "972"}, "IT": {"country": "Italy", "dial_code": "39"}, "JM": {"country": "Jamaica", "dial_code": "1876"}, "JP": {"country": "Japan", "dial_code": "81"}, "JE": {"country": "Jersey", "dial_code": "44"}, "JO": {"country": "Jordan", "dial_code": "962"}, "KZ": {"country": "Kazakhstan", "dial_code": "77"}, "KE": {"country": "Kenya", "dial_code": "254"}, "KI": {"country": "Kiribati", "dial_code": "686"}, "KP": {"country": "Democratic People's Republic of Korea", "dial_code": "850"}, "KR": {"country": "Republic of South Korea", "dial_code": "82"}, "KW": {"country": "Kuwait", "dial_code": "965"}, "KG": {"country": "Kyrgyzstan", "dial_code": "996"}, "LA": {"country": "Laos", "dial_code": "856"}, "LV": {"country": "Latvia", "dial_code": "371"}, "LB": {"country": "Lebanon", "dial_code": "961"}, "LS": {"country": "Lesotho", "dial_code": "266"}, "LR": {"country": "Liberia", "dial_code": "231"}, "LY": {"country": "Libyan Arab Jam<PERSON>riya", "dial_code": "218"}, "LI": {"country": "Liechtenstein", "dial_code": "423"}, "LT": {"country": "Lithuania", "dial_code": "370"}, "LU": {"country": "Luxembourg", "dial_code": "352"}, "MO": {"country": "Macao", "dial_code": "853"}, "MK": {"country": "Macedonia", "dial_code": "389"}, "MG": {"country": "Madagascar", "dial_code": "261"}, "MW": {"country": "Malawi", "dial_code": "265"}, "MY": {"country": "Malaysia", "dial_code": "60"}, "MV": {"country": "Maldives", "dial_code": "960"}, "ML": {"country": "Mali", "dial_code": "223"}, "MT": {"country": "Malta", "dial_code": "356"}, "MH": {"country": "Marshall Islands", "dial_code": "692"}, "MQ": {"country": "Martinique", "dial_code": "596"}, "MR": {"country": "Mauritania", "dial_code": "222"}, "MU": {"country": "Mauritius", "dial_code": "230"}, "YT": {"country": "Mayotte", "dial_code": "262"}, "MX": {"country": "Mexico", "dial_code": "52"}, "FM": {"country": "Federated States of Micronesia", "dial_code": "691"}, "MD": {"country": "Moldova", "dial_code": "373"}, "MC": {"country": "Monaco", "dial_code": "377"}, "MN": {"country": "Mongolia", "dial_code": "976"}, "ME": {"country": "Montenegro", "dial_code": "382"}, "MS": {"country": "Montserrat", "dial_code": "1664"}, "MA": {"country": "Morocco", "dial_code": "212"}, "MZ": {"country": "Mozambique", "dial_code": "258"}, "MM": {"country": "Myanmar", "dial_code": "95"}, "NA": {"country": "Namibia", "dial_code": "264"}, "NR": {"country": "Nauru", "dial_code": "674"}, "NP": {"country": "Nepal", "dial_code": "977"}, "NL": {"country": "Netherlands", "dial_code": "31"}, "AN": {"country": "Netherlands Antilles", "dial_code": "599"}, "NC": {"country": "New Caledonia", "dial_code": "687"}, "NZ": {"country": "New Zealand", "dial_code": "64"}, "NI": {"country": "Nicaragua", "dial_code": "505"}, "NE": {"country": "Niger", "dial_code": "227"}, "NG": {"country": "Nigeria", "dial_code": "234"}, "NU": {"country": "Niue", "dial_code": "683"}, "NF": {"country": "Norfolk Island", "dial_code": "672"}, "MP": {"country": "Northern Mariana Islands", "dial_code": "1670"}, "NO": {"country": "Norway", "dial_code": "47"}, "OM": {"country": "Oman", "dial_code": "968"}, "PK": {"country": "Pakistan", "dial_code": "92"}, "PW": {"country": "<PERSON><PERSON>", "dial_code": "680"}, "PS": {"country": "Palestinian Territory", "dial_code": "970"}, "PA": {"country": "Panama", "dial_code": "507"}, "PG": {"country": "Papua New Guinea", "dial_code": "675"}, "PY": {"country": "Paraguay", "dial_code": "595"}, "PE": {"country": "Peru", "dial_code": "51"}, "PH": {"country": "Philippines", "dial_code": "63"}, "PN": {"country": "Pitcairn", "dial_code": "872"}, "PL": {"country": "Poland", "dial_code": "48"}, "PT": {"country": "Portugal", "dial_code": "351"}, "PR": {"country": "Puerto Rico", "dial_code": "1939"}, "QA": {"country": "Qatar", "dial_code": "974"}, "RO": {"country": "Romania", "dial_code": "40"}, "RU": {"country": "Russia", "dial_code": "7"}, "RW": {"country": "Rwanda", "dial_code": "250"}, "RE": {"country": "Reunion", "dial_code": "262"}, "BL": {"country": "<PERSON>", "dial_code": "590"}, "SH": {"country": "Saint Helena", "dial_code": "290"}, "KN": {"country": "Saint Kitts and Nevis", "dial_code": "1869"}, "LC": {"country": "Saint Lucia", "dial_code": "1758"}, "MF": {"country": "Saint <PERSON>", "dial_code": "590"}, "PM": {"country": "Saint Pierre and Miquelon", "dial_code": "508"}, "VC": {"country": "Saint Vincent and the Grenadines", "dial_code": "1784"}, "WS": {"country": "Samoa", "dial_code": "685"}, "SM": {"country": "San Marino", "dial_code": "378"}, "ST": {"country": "Sao Tome and Principe", "dial_code": "239"}, "SA": {"country": "Saudi Arabia", "dial_code": "966"}, "SN": {"country": "Senegal", "dial_code": "221"}, "RS": {"country": "Serbia", "dial_code": "381"}, "SC": {"country": "Seychelles", "dial_code": "248"}, "SL": {"country": "Sierra Leone", "dial_code": "232"}, "SG": {"country": "Singapore", "dial_code": "65"}, "SK": {"country": "Slovakia", "dial_code": "421"}, "SI": {"country": "Slovenia", "dial_code": "386"}, "SB": {"country": "Solomon Islands", "dial_code": "677"}, "SO": {"country": "Somalia", "dial_code": "252"}, "ZA": {"country": "South Africa", "dial_code": "27"}, "SS": {"country": "South Sudan", "dial_code": "211"}, "GS": {"country": "South Georgia and the South Sandwich Islands", "dial_code": "500"}, "ES": {"country": "Spain", "dial_code": "34"}, "LK": {"country": "Sri Lanka", "dial_code": "94"}, "SD": {"country": "Sudan", "dial_code": "249"}, "SR": {"country": "Suricountry", "dial_code": "597"}, "SJ": {"country": "Svalbard and <PERSON>", "dial_code": "47"}, "SZ": {"country": "Swaziland", "dial_code": "268"}, "SE": {"country": "Sweden", "dial_code": "46"}, "CH": {"country": "Switzerland", "dial_code": "41"}, "SY": {"country": "Syrian Arab Republic", "dial_code": "963"}, "TW": {"country": "Taiwan", "dial_code": "886"}, "TJ": {"country": "Tajikistan", "dial_code": "992"}, "TZ": {"country": "Tanzania", "dial_code": "255"}, "TH": {"country": "Thailand", "dial_code": "66"}, "TL": {"country": "Timor-Leste", "dial_code": "670"}, "TG": {"country": "Togo", "dial_code": "228"}, "TK": {"country": "Tokelau", "dial_code": "690"}, "TO": {"country": "Tonga", "dial_code": "676"}, "TT": {"country": "Trinidad and Tobago", "dial_code": "1868"}, "TN": {"country": "Tunisia", "dial_code": "216"}, "TR": {"country": "Turkey", "dial_code": "90"}, "TM": {"country": "Turkmenistan", "dial_code": "993"}, "TC": {"country": "Turks and Caicos Islands", "dial_code": "1649"}, "TV": {"country": "Tuvalu", "dial_code": "688"}, "UG": {"country": "Uganda", "dial_code": "256"}, "UA": {"country": "Ukraine", "dial_code": "380"}, "AE": {"country": "United Arab Emirates", "dial_code": "971"}, "GB": {"country": "United Kingdom", "dial_code": "44"}, "US": {"country": "United States", "dial_code": "1"}, "UY": {"country": "Uruguay", "dial_code": "598"}, "UZ": {"country": "Uzbekistan", "dial_code": "998"}, "VU": {"country": "Vanuatu", "dial_code": "678"}, "VE": {"country": "Venezuela", "dial_code": "58"}, "VN": {"country": "Vietnam", "dial_code": "84"}, "VG": {"country": "British Virgin Islands", "dial_code": "1284"}, "VI": {"country": "U.S. Virgin Islands", "dial_code": "1340"}, "WF": {"country": "Wallis and Futuna", "dial_code": "681"}, "YE": {"country": "Yemen", "dial_code": "967"}, "ZM": {"country": "Zambia", "dial_code": "260"}, "ZW": {"country": "Zimbabwe", "dial_code": "263"}}