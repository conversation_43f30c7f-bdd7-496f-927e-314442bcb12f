@import url(./reset.css);

/* global css start */
.text--shadow {
  text-shadow: 1px 2px 5px rgba(0, 0, 0, 0.35);
}

.box--shadow1 {
  box-shadow: 0px 5px 26px -5px #cdd4e7 !important;
}

.box--shadow2 {
  box-shadow: 0 4px 10px #38414a0f !important;
}

.box--shadow3 {
  box-shadow: 0 3px 5px 0 rgba(18, 38, 63, 0.2) !important;
}

.b-radius--3 {
  border-radius: 3px !important;
  -webkit-border-radius: 3px !important;
  -moz-border-radius: 3px !important;
  -ms-border-radius: 3px !important;
  -o-border-radius: 3px !important;
}

.b-radius--4 {
  border-radius: 4px !important;
  -webkit-border-radius: 4px !important;
  -moz-border-radius: 4px !important;
  -ms-border-radius: 4px !important;
  -o-border-radius: 4px !important;
}

.b-radius--5 {
  border-radius: 5px !important;
  -webkit-border-radius: 5px !important;
  -moz-border-radius: 5px !important;
  -ms-border-radius: 5px !important;
  -o-border-radius: 5px !important;
}

.b-radius--6 {
  border-radius: 6px !important;
  -webkit-border-radius: 6px !important;
  -moz-border-radius: 6px !important;
  -ms-border-radius: 6px !important;
  -o-border-radius: 6px !important;
}

.b-radius--7 {
  border-radius: 7px !important;
  -webkit-border-radius: 7px !important;
  -moz-border-radius: 7px !important;
  -ms-border-radius: 7px !important;
  -o-border-radius: 7px !important;
}

.b-radius--8 {
  border-radius: 8px !important;
  -webkit-border-radius: 8px !important;
  -moz-border-radius: 8px !important;
  -ms-border-radius: 8px !important;
  -o-border-radius: 8px !important;
}

.b-radius--9 {
  border-radius: 9px !important;
  -webkit-border-radius: 9px !important;
  -moz-border-radius: 9px !important;
  -ms-border-radius: 9px !important;
  -o-border-radius: 9px !important;
}

.b-radius--10 {
  border-radius: 10px !important;
  -webkit-border-radius: 10px !important;
  -moz-border-radius: 10px !important;
  -ms-border-radius: 10px !important;
  -o-border-radius: 10px !important;
}

.b-radius--rounded {
  border-radius: 50% !important;
  -webkit-border-radius: 50% !important;
  -moz-border-radius: 50% !important;
  -ms-border-radius: 50% !important;
  -o-border-radius: 50% !important;
}

.b-radius--capsule {
  border-radius: 999px !important;
  -webkit-border-radius: 999px !important;
  -moz-border-radius: 999px !important;
  -ms-border-radius: 999px !important;
  -o-border-radius: 999px !important;
}

.b-radius--none {
  border-radius: 0 !important;
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  -ms-border-radius: 0 !important;
  -o-border-radius: 0 !important;
}

.nav-tabs-primary {
  border: none;
}

.nav-tabs-primary .nav-item a {
  border: none;
}

.nav-tabs-primary .nav-item a.active {
  border-bottom: 2px solid #4634ff;
}

.hover--effect1 {
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.hover--effect1:hover {
  -webkit-transform: scale(1.05, 1.05);
  -ms-transform: scale(1.05, 1.05);
  transform: scale(1.05, 1.05);
}

.w--10 {
  width: 10% !important;
}

.w--15 {
  width: 15% !important;
}

.w--20 {
  width: 20% !important;
}

.page-wrapper {
  min-height: 100vh;
}

/* global css end */
/* typography css start */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 500;
  color: #34495e;
  margin: 0;
  line-height: 1.4;
}

h1 {
  font-size: 2.25rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.375rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.125rem;
}

h6 {
  font-size: 1rem;
}

p {
  font-size: 0.875rem;
  line-height: 1.7;
  font-weight: 400;
}

p,
li,
a {
  font-size: 0.875rem;
}

.text--small {
  font-size: 0.75rem !important;
}

.f-size--24 {
  font-size: 1.5rem !important;
}

.f-size--28 {
  font-size: 1.75rem !important;
}

.f-size--42 {
  font-size: 2.625rem !important;
}

.f-size--48 {
  font-size: 3rem !important;
}

.f-size--56 {
  font-size: 3.5rem !important;
}

.f-size--72 {
  font-size: 4.5rem !important;
}

.f-size--90 {
  font-size: 5.625rem !important;
}

.f-size--100 {
  font-size: 6.25rem !important;
}

/* typography css end */
/* color css start */
.text--primary {
  color: #4634ff !important;
}

.text--secondary {
  color: #868e96 !important;
}

.text--success {
  color: #28c76f !important;
}

.text--danger {
  color: #eb2222 !important;
}

.text--warning {
  color: #ff9f43 !important;
}

.text--info {
  color: #1e9ff2 !important;
}

.text--dark {
  color: #10163a !important;
}

.text--white {
  color: #ffffff !important;
}

.text--black {
  color: #000000 !important;
}

.text--gray {
  color: #9e9e9e !important;
}

.text--blue-gray {
  color: #607d8b !important;
}

.text--red {
  color: #f44336 !important;
}

.text--pink {
  color: #e91e63 !important;
}

.text--purple {
  color: #9c27b0 !important;
}

.text--deep-purple {
  color: #673ab7 !important;
}

.text--indigo {
  color: #3f51b5 !important;
}

.text--blue {
  color: #2196f3 !important;
}

.text--light-blue {
  color: #45c5ff !important;
}

.text--cyan {
  color: #00bcd4 !important;
}

.text--teal {
  color: #009688 !important;
}

.text--green {
  color: #4caf50 !important;
}

.text--light-green {
  color: #8bc34a !important;
}

.text--lime {
  color: #cddc39 !important;
}

.text--yellow {
  color: #ffeb3b !important;
}

.text--amber {
  color: #ffc107 !important;
}

.text--orange {
  color: #ff9800 !important;
}

.text--brown {
  color: #795548 !important;
}

.text-color--1 {
  color: #127681 !important;
}

.text-color--2 {
  color: #ea907a !important;
}

.text-color--3 {
  color: #10375c !important;
}

.text-color--4 {
  color: #4f8a8b !important;
}

.text-color--5 {
  color: #d92027 !important;
}

.text-color--6 {
  color: #ff9234 !important;
}

.text-color--7 {
  color: #4cd3c2 !important;
}

.text-color--8 {
  color: #35d0ba !important;
}

.text-color--9 {
  color: #e84a5f !important;
}

.text-color--10 {
  color: #00005c !important;
}

.text-color--11 {
  color: #45046a !important;
}

.text-color--12 {
  color: #5c2a9d !important;
}

.text-color--13 {
  color: #6a097d !important;
}

.text-color--14 {
  color: #ff5200 !important;
}

.text-color--15 {
  color: #162447 !important;
}

.text-color--16 {
  color: #e43f5a !important;
}

.text-color--17 {
  color: #035aa6 !important;
}

.text-color--18 {
  color: #0779e4 !important;
}

.text-color--19 {
  color: #342ead !important;
}

.text-color--20 {
  color: #d7385e !important;
}

/* color css end */
/* background-color css start */
*[class*='bg'] {
  color: #ffffff;
}

.bg--white,
.bg--gray {
  color: inherit;
}

.bg--primary {
  --color: #4634ff;
  background-color: var(--color) !important;
}

.bg--secondary {
  --color: #868e96;
  background-color: var(--color) !important;
}

.bg--success {
  --color: #28c76f;
  background-color: var(--color) !important;
}

.bg--danger {
  --color: #eb2222;
  background-color: var(--color) !important;
}

.bg--warning {
  --color: #ff9f43;
  background-color: var(--color) !important;
}

.bg--info {
  --color: #1e9ff2;
  background-color: var(--color) !important;
}

.bg--dark {
  --color: #071251;
  background-color: var(--color) !important;
}

.bg--white {
  --color: #ffffff;
  background-color: var(--color) !important;
}

.bg--black {
  --color: #000000;
  background-color: var(--color) !important;
}

.bg--gray {
  --color: #9e9e9e;
  background-color: var(--color) !important;
}

.bg--blue-gray {
  --color: #607d8b;
  background-color: var(--color) !important;
}

.bg--red {
  --color: #f44336;
  background-color: var(--color) !important;
}

.bg--pink {
  --color: #e91e63;
  background-color: var(--color) !important;
}

.bg--purple {
  --color: #9c27b0;
  background-color: var(--color) !important;
}

.bg--deep-purple {
  --color: #673ab7;
  background-color: var(--color) !important;
}

.bg--indigo {
  --color: #3f51b5;
  background-color: var(--color) !important;
}

.bg--blue {
  --color: #2196f3;
  background-color: var(--color) !important;
}

.bg--light-blue {
  --color: #45c5ff;
  background-color: var(--color) !important;
}

.bg--cyan {
  --color: #00bcd4;
  background-color: var(--color) !important;
}

.bg--teal {
  --color: #009688;
  background-color: var(--color) !important;
}

.bg--green {
  --color: #4caf50;
  background-color: var(--color) !important;
}

.bg--light-green {
  --color: #8bc34a;
  background-color: var(--color) !important;
}

.bg--lime {
  --color: #cddc39;
  background-color: var(--color) !important;
}

.bg--yellow {
  --color: #ffeb3b;
  background-color: var(--color) !important;
}

.bg--amber {
  --color: #ffc107;
  background-color: var(--color) !important;
}

.bg--orange {
  --color: #ff9800;
  background-color: var(--color) !important;
}

.bg--brown {
  --color: #795548;
  background-color: var(--color) !important;
}

.bg--transparent {
  background-color: transparent;
  color: #5b6e88;
}

.bg--1 {
  --color: #127681;
  background-color: var(--color) !important;
}

.bg--2 {
  --color: #ea907a;
  background-color: var(--color) !important;
}

.bg--3 {
  --color: #10375c;
  background-color: var(--color) !important;
}

.bg--4 {
  --color: #4f8a8b;
  background-color: var(--color) !important;
}

.bg--5 {
  --color: #d92027;
  background-color: var(--color) !important;
}

.bg--6 {
  --color: #ff9234;
  background-color: var(--color) !important;
}

.bg--7 {
  --color: #4cd3c2;
  background-color: var(--color) !important;
}

.bg--8 {
  --color: #35d0ba;
  background-color: var(--color) !important;
}

.bg--9 {
  --color: #e84a5f;
  background-color: var(--color) !important;
}

.bg--10 {
  --color: #00005c;
  background-color: var(--color) !important;
}

.bg--11 {
  --color: #45046a;
  background-color: var(--color) !important;
}

.bg--12 {
  --color: #5c2a9d;
  background-color: var(--color) !important;
}

.bg--13 {
  --color: #6a097d;
  background-color: var(--color) !important;
}

.bg--14 {
  --color: #ff5200;
  background-color: var(--color) !important;
}

.bg--15 {
  --color: #162447;
  background-color: var(--color) !important;
}

.bg--16 {
  --color: #e43f5a;
  background-color: var(--color) !important;
}

.bg--17 {
  --color: #035aa6;
  background-color: var(--color) !important;
}

.bg--18 {
  --color: #0779e4;
  background-color: var(--color) !important;
}

.bg--19 {
  --color: #342ead;
  background-color: var(--color) !important;
}

.bg--20 {
  --color: #d7385e;
  background-color: var(--color) !important;
}

.bg--gradi-1,
.overlay--gradi-1[class*='overlay']::before {
  background: #4776e6 !important;
  background: -webkit-linear-gradient(to top, #8e54e9, #4776e6) !important;
  background: linear-gradient(to top, #8e54e9, #4776e6) !important;
}

.bg--gradi-2,
.overlay--gradi-2[class*='overlay']::before {
  background: #c31432 !important;
  background: -webkit-linear-gradient(to right, #240b36, #c31432) !important;
  background: linear-gradient(to right, #240b36, #c31432) !important;
}

.bg--gradi-3,
.overlay--gradi-3[class*='overlay']::before {
  background: #8360c3 !important;
  background: -webkit-linear-gradient(to right, #2ebf91, #8360c3) !important;
  background: linear-gradient(to right, #2ebf91, #8360c3) !important;
}

.bg--gradi-4,
.overlay--gradi-4[class*='overlay']::before {
  background: #654ea3 !important;
  background: -webkit-linear-gradient(to right, #eaafc8, #654ea3) !important;
  background: linear-gradient(to right, #eaafc8, #654ea3) !important;
}

.bg--gradi-5,
.overlay--gradi-5[class*='overlay']::before {
  background: #da4453 !important;
  background: -webkit-linear-gradient(to left, #89216b, #da4453) !important;
  background: linear-gradient(to left, #89216b, #da4453) !important;
}

.bg--gradi-6,
.overlay--gradi-6[class*='overlay']::before {
  background: #11998e !important;
  background: -webkit-linear-gradient(to right, #38ef7d, #11998e) !important;
  background: linear-gradient(to right, #38ef7d, #11998e) !important;
}

.bg--gradi-7,
.overlay--gradi-7[class*='overlay']::before {
  background: #396afc !important;
  background: -webkit-linear-gradient(to bottom, #2948ff, #396afc) !important;
  background: linear-gradient(to bottom, #2948ff, #396afc) !important;
}

.bg--gradi-8,
.overlay--gradi-8[class*='overlay']::before {
  background: #0575e6 !important;
  background: -webkit-linear-gradient(to bottom, #021b79, #0575e6) !important;
  background: linear-gradient(to bottom, #021b79, #0575e6) !important;
}

.bg--gradi-9,
.overlay--gradi-9[class*='overlay']::before {
  background: #36d1dc !important;
  background: -webkit-linear-gradient(to bottom, #5b86e5, #36d1dc) !important;
  background: linear-gradient(to bottom, #5b86e5, #36d1dc) !important;
}

.bg--gradi-10,
.overlay--gradi-10[class*='overlay']::before {
  background: #4568dc !important;
  background: -webkit-linear-gradient(to bottom, #b06ab3, #4568dc) !important;
  background: linear-gradient(to bottom, #b06ab3, #4568dc) !important;
}

.bg--gradi-11,
.overlay--gradi-11[class*='overlay']::before {
  background: #ee0979 !important;
  background: -webkit-linear-gradient(to bottom, #ff6a00, #ee0979) !important;
  background: linear-gradient(to bottom, #ff6a00, #ee0979) !important;
}

.bg--gradi-12,
.overlay--gradi-12[class*='overlay']::before {
  background: #41295a !important;
  background: -webkit-linear-gradient(to bottom, #2f0743, #41295a) !important;
  background: linear-gradient(to bottom, #2f0743, #41295a) !important;
}

.bg--gradi-13,
.overlay--gradi-13[class*='overlay']::before {
  background: #4ecdc4 !important;
  background: -webkit-linear-gradient(to bottom, #556270, #4ecdc4) !important;
  background: linear-gradient(to bottom, #556270, #4ecdc4) !important;
}

.bg--gradi-14,
.overlay--gradi-14[class*='overlay']::before {
  background: #f85032 !important;
  background: -webkit-linear-gradient(to bottom, #e73827, #f85032) !important;
  background: linear-gradient(to bottom, #e73827, #f85032) !important;
}

.bg--gradi-15,
.overlay--gradi-15[class*='overlay']::before {
  background: #cb2d3e !important;
  background: -webkit-linear-gradient(to bottom, #ef473a, #cb2d3e) !important;
  background: linear-gradient(to bottom, #ef473a, #cb2d3e) !important;
}

.bg--gradi-16,
.overlay--gradi-16[class*='overlay']::before {
  background: #56ab2f !important;
  background: -webkit-linear-gradient(to bottom, #a8e063, #56ab2f) !important;
  background: linear-gradient(to bottom, #a8e063, #56ab2f) !important;
}

.bg--gradi-17,
.overlay--gradi-17[class*='overlay']::before {
  background: #000428 !important;
  background: -webkit-linear-gradient(to bottom, #004e92, #000428) !important;
  background: linear-gradient(to bottom, #004e92, #000428) !important;
}

.bg--gradi-18,
.overlay--gradi-18[class*='overlay']::before {
  background: #42275a !important;
  background: -webkit-linear-gradient(to bottom, #734b6d, #42275a) !important;
  background: linear-gradient(to bottom, #734b6d, #42275a) !important;
}

.bg--gradi-19,
.overlay--gradi-19[class*='overlay']::before {
  background: #141e30 !important;
  background: -webkit-linear-gradient(to bottom, #243b55, #141e30) !important;
  background: linear-gradient(to bottom, #243b55, #141e30) !important;
}

.bg--gradi-20,
.overlay--gradi-20[class*='overlay']::before {
  background: #2c3e50 !important;
  background: -webkit-linear-gradient(to bottom, #4ca1af, #2c3e50) !important;
  background: linear-gradient(to bottom, #4ca1af, #2c3e50) !important;
}

.bg--gradi-21,
.overlay--gradi-21[class*='overlay']::before {
  background: #3a7bd5 !important;
  background: -webkit-linear-gradient(to bottom, #3a6073, #3a7bd5) !important;
  background: linear-gradient(to bottom, #3a6073, #3a7bd5) !important;
}

.bg--gradi-21,
.overlay--gradi-21[class*='overlay']::before {
  background: #ff4b1f !important;
  background: -webkit-linear-gradient(to bottom, #ff9068, #ff4b1f) !important;
  background: linear-gradient(to bottom, #ff9068, #ff4b1f) !important;
}

.bg--gradi-22,
.overlay--gradi-22[class*='overlay']::before {
  background: #4b79a1 !important;
  background: -webkit-linear-gradient(to bottom, #283e51, #4b79a1) !important;
  background: linear-gradient(to bottom, #283e51, #4b79a1) !important;
}

.bg--gradi-23,
.overlay--gradi-23[class*='overlay']::before {
  background: #2980b9 !important;
  background: -webkit-linear-gradient(to bottom, #2c3e50, #2980b9) !important;
  background: linear-gradient(to bottom, #2c3e50, #2980b9) !important;
}

.bg--gradi-24,
.overlay--gradi-24[class*='overlay']::before {
  background: #1e3c72 !important;
  background: -webkit-linear-gradient(to bottom, #2a5298, #1e3c72) !important;
  background: linear-gradient(to bottom, #2a5298, #1e3c72) !important;
}

.bg--gradi-25,
.overlay--gradi-25[class*='overlay']::before {
  background: #fd746c !important;
  background: -webkit-linear-gradient(to bottom, #ff9068, #fd746c) !important;
  background: linear-gradient(to bottom, #ff9068, #fd746c) !important;
}

.bg--gradi-26,
.overlay--gradi-26[class*='overlay']::before {
  background: #6a3093 !important;
  background: -webkit-linear-gradient(to bottom, #a044ff, #6a3093) !important;
  background: linear-gradient(to bottom, #a044ff, #6a3093) !important;
}

.bg--gradi-27,
.overlay--gradi-27[class*='overlay']::before {
  background: #457fca !important;
  background: -webkit-linear-gradient(to bottom, #5691c8, #457fca) !important;
  background: linear-gradient(to bottom, #5691c8, #457fca) !important;
}

.bg--gradi-28,
.overlay--gradi-28[class*='overlay']::before {
  background: #b24592 !important;
  background: -webkit-linear-gradient(to bottom, #f15f79, #b24592) !important;
  background: linear-gradient(to bottom, #f15f79, #b24592) !important;
}

.bg--gradi-29,
.overlay--gradi-29[class*='overlay']::before {
  background: #ffb75e !important;
  background: -webkit-linear-gradient(to bottom, #ed8f03, #ffb75e) !important;
  background: linear-gradient(to bottom, #ed8f03, #ffb75e) !important;
}

.bg--gradi-30,
.overlay--gradi-30[class*='overlay']::before {
  background: #8e0e00 !important;
  background: -webkit-linear-gradient(to bottom, #1f1c18, #8e0e00) !important;
  background: linear-gradient(to bottom, #1f1c18, #8e0e00) !important;
}

.bg--gradi-31,
.overlay--gradi-31[class*='overlay']::before {
  background: #76b852 !important;
  background: -webkit-linear-gradient(to bottom, #8dc26f, #76b852) !important;
  background: linear-gradient(to bottom, #8dc26f, #76b852) !important;
}

.bg--gradi-32,
.overlay--gradi-32[class*='overlay']::before {
  background: #673ab7 !important;
  background: -webkit-linear-gradient(to bottom, #512da8, #673ab7) !important;
  background: linear-gradient(to bottom, #512da8, #673ab7) !important;
}

.bg--gradi-33,
.overlay--gradi-33[class*='overlay']::before {
  background: #f46b45 !important;
  background: -webkit-linear-gradient(to bottom, #eea849, #f46b45) !important;
  background: linear-gradient(to bottom, #eea849, #f46b45) !important;
}

.bg--gradi-34,
.overlay--gradi-34[class*='overlay']::before {
  background: #005c97 !important;
  background: -webkit-linear-gradient(to bottom, #363795, #005c97) !important;
  background: linear-gradient(to bottom, #363795, #005c97) !important;
}

.bg--gradi-35,
.overlay--gradi-35[class*='overlay']::before {
  background: #e53935 !important;
  background: -webkit-linear-gradient(to bottom, #e35d5b, #e53935) !important;
  background: linear-gradient(to bottom, #e35d5b, #e53935) !important;
}

.bg--gradi-36,
.overlay--gradi-36[class*='overlay']::before {
  background: #2c3e50 !important;
  background: -webkit-linear-gradient(to bottom, #3498db, #2c3e50) !important;
  background: linear-gradient(to bottom, #3498db, #2c3e50) !important;
}

.bg--gradi-37,
.overlay--gradi-37[class*='overlay']::before {
  background: #6a9113 !important;
  background: -webkit-linear-gradient(to bottom, #141517, #6a9113) !important;
  background: linear-gradient(to bottom, #141517, #6a9113) !important;
}

.bg--gradi-38,
.overlay--gradi-38[class*='overlay']::before {
  background: #136a8a !important;
  background: -webkit-linear-gradient(to bottom, #267871, #136a8a) !important;
  background: linear-gradient(to bottom, #267871, #136a8a) !important;
}

.bg--gradi-39,
.overlay--gradi-39[class*='overlay']::before {
  background: #6441a5 !important;
  background: -webkit-linear-gradient(to bottom, #2a0845, #6441a5) !important;
  background: linear-gradient(to bottom, #2a0845, #6441a5) !important;
}

.bg--gradi-40,
.overlay--gradi-40[class*='overlay']::before {
  background: #43cea2 !important;
  background: -webkit-linear-gradient(to bottom, #185a9d, #43cea2) !important;
  background: linear-gradient(to bottom, #185a9d, #43cea2) !important;
}

.bg--gradi-41,
.overlay--gradi-41[class*='overlay']::before {
  background: #00c6ff !important;
  background: -webkit-linear-gradient(to bottom, #0072ff, #00c6ff) !important;
  background: linear-gradient(to bottom, #0072ff, #00c6ff) !important;
}

.bg--gradi-42,
.overlay--gradi-42[class*='overlay']::before {
  background: #9d50bb !important;
  background: -webkit-linear-gradient(to bottom, #6e48aa, #9d50bb) !important;
  background: linear-gradient(to bottom, #6e48aa, #9d50bb) !important;
}

.bg--gradi-43,
.overlay--gradi-43[class*='overlay']::before {
  background: #add100 !important;
  background: -webkit-linear-gradient(to bottom, #7b920a, #add100) !important;
  background: linear-gradient(to bottom, #7b920a, #add100) !important;
}

.bg--gradi-44,
.overlay--gradi-44[class*='overlay']::before {
  background: #00d2ff !important;
  background: -webkit-linear-gradient(to bottom, #3a7bd5, #00d2ff) !important;
  background: linear-gradient(to bottom, #3a7bd5, #00d2ff) !important;
}

.bg--gradi-45,
.overlay--gradi-45[class*='overlay']::before {
  background: #a73737 !important;
  background: -webkit-linear-gradient(to bottom, #7a2828, #a73737) !important;
  background: linear-gradient(to bottom, #7a2828, #a73737) !important;
}

.bg--gradi-46,
.overlay--gradi-46[class*='overlay']::before {
  background: #4b6cb7 !important;
  background: -webkit-linear-gradient(to bottom, #182848, #4b6cb7) !important;
  background: linear-gradient(to bottom, #182848, #4b6cb7) !important;
}

.bg--gradi-47,
.overlay--gradi-47[class*='overlay']::before {
  background: #e43a15 !important;
  background: -webkit-linear-gradient(to bottom, #e65245, #e43a15) !important;
  background: linear-gradient(to bottom, #e65245, #e43a15) !important;
}

.bg--gradi-48,
.overlay--gradi-48[class*='overlay']::before {
  background: #c04848 !important;
  background: -webkit-linear-gradient(to bottom, #480048, #c04848) !important;
  background: linear-gradient(to bottom, #480048, #c04848) !important;
}

.bg--gradi-49,
.overlay--gradi-49[class*='overlay']::before {
  background: #232526 !important;
  background: -webkit-linear-gradient(to bottom, #414345, #232526) !important;
  background: linear-gradient(to bottom, #414345, #232526) !important;
}

.bg--gradi-50,
.overlay--gradi-50[class*='overlay']::before {
  background: #4776e6 !important;
  background: -webkit-linear-gradient(to bottom, #8e54e9, #4776e6) !important;
  background: linear-gradient(to bottom, #8e54e9, #4776e6) !important;
}

/* background-color css end */
/* overlay css start */
[class*='overlay'] {
  position: relative;
  z-index: 1;
}

[class*='overlay']:not(.overlay-icon)::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.85;
  z-index: -10;
}

[class*='overlay'].overlay--primary::before {
  background-color: #4634ff !important;
}

[class*='overlay'].overlay--secondary::before {
  background-color: #868e96 !important;
}

[class*='overlay'].overlay--success::before {
  background-color: #28c76f !important;
}

[class*='overlay'].overlay--danger::before {
  background-color: #eb2222 !important;
}

[class*='overlay'].overlay--warning::before {
  background-color: #ff9f43 !important;
}

[class*='overlay'].overlay--info::before {
  background-color: #1e9ff2 !important;
}

[class*='overlay'].overlay--dark::before {
  background-color: #10163a !important;
}

[class*='overlay'].overlay--white::before {
  background-color: #ffffff !important;
}

[class*='overlay'].overlay--black::before {
  background-color: #000000 !important;
}

[class*='overlay'].overlay--gray::before {
  background-color: #9e9e9e !important;
}

[class*='overlay'].overlay--blue-gray::before {
  background-color: #607d8b !important;
}

[class*='overlay'].overlay--red::before {
  background-color: #f44336 !important;
}

[class*='overlay'].overlay--pink::before {
  background-color: #e91e63 !important;
}

[class*='overlay'].overlay--purple::before {
  background-color: #9c27b0 !important;
}

[class*='overlay'].overlay--deep-purple::before {
  background-color: #673ab7 !important;
}

[class*='overlay'].overlay--indigo::before {
  background-color: #3f51b5 !important;
}

[class*='overlay'].overlay--blue::before {
  background-color: #2196f3 !important;
}

[class*='overlay'].overlay--light-blue::before {
  background-color: #45c5ff !important;
}

[class*='overlay'].overlay--cyan::before {
  background-color: #00bcd4 !important;
}

[class*='overlay'].overlay--teal::before {
  background-color: #009688 !important;
}

[class*='overlay'].overlay--green::before {
  background-color: #4caf50 !important;
}

[class*='overlay'].overlay--light-green::before {
  background-color: #8bc34a !important;
}

[class*='overlay'].overlay--lime::before {
  background-color: #cddc39 !important;
}

[class*='overlay'].overlay--yellow::before {
  background-color: #ffeb3b !important;
}

[class*='overlay'].overlay--amber::before {
  background-color: #ffc107 !important;
}

[class*='overlay'].overlay--orange::before {
  background-color: #ff9800 !important;
}

[class*='overlay'].overlay--brown::before {
  background-color: #795548 !important;
}

.overlay--opacity-1::before {
  opacity: 0.1 !important;
}

.overlay--opacity-2::before {
  opacity: 0.2 !important;
}

.overlay--opacity-3::before {
  opacity: 0.3 !important;
}

.overlay--opacity-4::before {
  opacity: 0.4 !important;
}

.overlay--opacity-5::before {
  opacity: 0.5 !important;
}

.overlay--opacity-6::before {
  opacity: 0.6 !important;
}

.overlay--overlay--opacity-7::before {
  opacity: 0.7 !important;
}

.overlay--opacity-8::before {
  opacity: 0.8 !important;
}

.overlay--opacity-9::before {
  opacity: 0.9 !important;
}

.overlay--opacity-10::before {
  opacity: 1 !important;
}

/* overlay css end */
/* outline color css start */
.b--1 {
  border: 1px solid !important;
}

.b--2 {
  border: 2px solid !important;
}

.b--3 {
  border: 3px solid !important;
}

.b--4 {
  border: 4px solid !important;
}

.b--5 {
  border: 5px solid !important;
}

.bt--1 {
  border: none;
  border-top: 1px solid !important;
}

.bt--2 {
  border-top: 2px solid !important;
}

.bt--3 {
  border-top: 3px solid !important;
}

.bt--4 {
  border-top: 4px solid !important;
}

.bt--5 {
  border-top: 5px solid !important;
}

.bb--1 {
  border-bottom: 1px solid !important;
}

.bb--2 {
  border-bottom: 2px solid !important;
}

.bb--3 {
  border-bottom: 3px solid !important;
}

.bb--4 {
  border-bottom: 4px solid !important;
}

.bb--5 {
  border-bottom: 5px solid !important;
}

.bl--1 {
  border-left: 1px solid !important;
}

.bl--2 {
  border-left: 2px solid !important;
}

.bl--3 {
  border-left: 3px solid !important;
}

.bl--4 {
  border-left: 4px solid !important;
}

.bl--5 {
  border-left: 5px solid !important;
}

.br--1 {
  border-right: 1px solid !important;
}

.br--2 {
  border-right: 2px solid !important;
}

.br--3 {
  border-right: 3px solid !important;
}

.br--4 {
  border-right: 4px solid !important;
}

.br--5 {
  border-right: 5px solid !important;
}

.bl--5-primary {
  border-left: 5px solid #4634ff !important;
}

.border--primary {
  border-color: #4634ff !important;
}

.border--secondary {
  border-color: #868e96 !important;
}

.border--success {
  border-color: #28c76f !important;
}

.border--danger {
  border-color: #eb2222 !important;
}

.border--warning {
  border-color: #ff9f43 !important;
}

.border--info {
  border-color: #1e9ff2 !important;
}

.border--dark {
  border-color: #10163a !important;
}

.border--white,
.border--light {
  border-color: #ffffff !important;
}

.border--black {
  border-color: #000000 !important;
}

.border--gray {
  border-color: #9e9e9e !important;
}

.border--blue-gray {
  border-color: #607d8b !important;
}

.border--red {
  border-color: #f44336 !important;
}

.border--pink {
  border-color: #e91e63 !important;
}

.border--purple {
  border-color: #9c27b0 !important;
}

.border--deep-purple {
  border-color: #673ab7 !important;
}

.border--indigo {
  border-color: #3f51b5 !important;
}

.border--blue {
  border-color: #2196f3 !important;
}

.border--light-blue {
  border-color: #45c5ff !important;
}

.border--cyan {
  border-color: #00bcd4 !important;
}

.border--teal {
  border-color: #009688 !important;
}

.border--green {
  border-color: #4caf50 !important;
}

.border--light-green {
  border-color: #8bc34a !important;
}

.border--lime {
  border-color: #cddc39 !important;
}

.border--yellow {
  border-color: #ffeb3b !important;
}

.border--amber {
  border-color: #ffc107 !important;
}

.border--orange {
  border-color: #ff9800 !important;
}

.border--brown {
  border-color: #795548 !important;
}

.b-color--1 {
  border-color: #127681 !important;
}

.b-color--2 {
  border-color: #ea907a !important;
}

.b-color--3 {
  border-color: #10375c !important;
}

.b-color--4 {
  border-color: #4f8a8b !important;
}

.b-color--5 {
  border-color: #d92027 !important;
}

.b-color--6 {
  border-color: #ff9234 !important;
}

.b-color--7 {
  border-color: #4cd3c2 !important;
}

.b-color--8 {
  border-color: #35d0ba !important;
}

.b-color--9 {
  border-color: #e84a5f !important;
}

.b-color--10 {
  border-color: #00005c !important;
}

.b-color--11 {
  border-color: #45046a !important;
}

.b-color--12 {
  border-color: #5c2a9d !important;
}

.b-color--13 {
  border-color: #6a097d !important;
}

.b-color--14 {
  border-color: #ff5200 !important;
}

.b-color--15 {
  border-color: #162447 !important;
}

.b-color--16 {
  border-color: #e43f5a !important;
}

.b-color--17 {
  border-color: #035aa6 !important;
}

.b-color--18 {
  border-color: #0779e4 !important;
}

.b-color--19 {
  border-color: #342ead !important;
}

.b-color--20 {
  border-color: #d7385e !important;
}

/* outline color css end */
/* button css start */
.button--group {
  margin: -5px -15px;
}

.button--group .btn {
  margin: 2px 3px;
}

.btn {
  --btn-color: #fff;
  border-radius: 3.2px;
}

.btn:hover:hover,
.btn:hover:active,
.btn:hover:focus,
.btn:hover:focus-visible {
  color: var(--btn-color) !important;
}

.btn i {
  margin-right: 5px;
}

.btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: none;
}

*[class*='btn-'] {
  transition: all 0.3s;
  font-size: 0.875rem;
}

.btn--capsule {
  border-radius: 999px;
  -webkit-border-radius: 999px;
  -moz-border-radius: 999px;
  -ms-border-radius: 999px;
  -o-border-radius: 999px;
}

.btn--primary {
  --color: #4634ff;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--primary:hover,
.btn--primary:active,
.btn--primary:focus,
.btn--primary:focus-visible {
  --color: #1801ff;
}

.btn-outline--primary {
  --color: #4634ff;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--primary:hover,
.btn-outline--primary:active,
.btn-outline--primary:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--secondary {
  --color: #868e96;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--secondary:hover,
.btn--secondary:active,
.btn--secondary:focus,
.btn--secondary:focus-visible {
  --color: #6c757d;
}

.btn-outline--secondary {
  --color: #868e96;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--secondary:hover,
.btn-outline--secondary:active,
.btn-outline--secondary:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--success {
  --color: #28c76f;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--success:hover,
.btn--success:active,
.btn--success:focus,
.btn--success:focus-visible {
  --color: #1f9d57;
}

.btn-outline--success {
  --color: #28c76f;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--success:hover,
.btn-outline--success:active,
.btn-outline--success:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--danger {
  --color: #eb2222;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--danger:hover,
.btn--danger:active,
.btn--danger:focus,
.btn--danger:focus-visible {
  --color: #c81212;
}

.btn-outline--danger {
  --color: #eb2222;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--danger:hover,
.btn-outline--danger:active,
.btn-outline--danger:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--warning {
  --color: #ff9f43;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--warning:hover,
.btn--warning:active,
.btn--warning:focus,
.btn--warning:focus-visible {
  --color: #ff8510;
}

.btn-outline--warning {
  --color: #ff9f43;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--warning:hover,
.btn-outline--warning:active,
.btn-outline--warning:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--info {
  --color: #1e9ff2;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--info:hover,
.btn--info:active,
.btn--info:focus,
.btn--info:focus-visible {
  --color: #0c84d1;
}

.btn-outline--info {
  --color: #1e9ff2;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--info:hover,
.btn-outline--info:active,
.btn-outline--info:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--dark {
  --color: #10163a;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--dark:hover,
.btn--dark:active,
.btn--dark:focus,
.btn--dark:focus-visible {
  --color: #050712;
}

.btn-outline--dark {
  --color: #10163a;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--dark:hover,
.btn-outline--dark:active,
.btn-outline--dark:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--gray {
  --color: #9e9e9e;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--gray:hover,
.btn--gray:active,
.btn--gray:focus,
.btn--gray:focus-visible {
  --color: #858585;
}

.btn-outline--gray {
  --color: #9e9e9e;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--gray:hover,
.btn-outline--gray:focus,
.btn-outline--gray:active,
.btn-outline--gray:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--blue-gray {
  --color: #607d8b;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--blue-gray:hover,
.btn--blue-gray:active,
.btn--blue-gray:focus,
.btn--blue-gray:focus-visible {
  --color: #4b626d;
}

.btn-outline--blue-gray {
  --color: #607d8b;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--blue-gray:hover,
.btn-outline--blue-gray:focus,
.btn-outline--blue-gray:active,
.btn-outline--blue-gray:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--red {
  --color: #f44336;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--red:hover,
.btn--red:active,
.btn--red:focus,
.btn--red:focus-visible {
  --color: #ea1c0d;
}

.btn-outline--red {
  --color: #f44336;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--red:hover,
.btn-outline--red:focus,
.btn-outline--red:active,
.btn-outline--red:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--pink {
  --color: #e91e63;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--pink:hover,
.btn--pink:active,
.btn--pink:focus,
.btn--pink:focus-visible {
  --color: #c1134e;
}

.btn-outline--pink {
  --color: #e91e63;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--pink:hover,
.btn-outline--pink:focus,
.btn-outline--pink:active,
.btn-outline--pink:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--purple {
  --color: #9c27b0;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--purple:hover,
.btn--purple:active,
.btn--purple:focus,
.btn--purple:focus-visible {
  --color: #771e86;
}

.btn-outline--purple {
  --color: #9c27b0;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--purple:hover,
.btn-outline--purple:focus,
.btn-outline--purple:active,
.btn-outline--purple:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--deep-purple {
  --color: #673ab7;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--deep-purple:hover,
.btn--deep-purple:active,
.btn--deep-purple:focus,
.btn--deep-purple:focus-visible {
  --color: #512e90;
}

.btn-outline--deep-purple {
  --color: #673ab7;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--deep-purple:hover,
.btn-outline--deep-purple:focus,
.btn-outline--deep-purple:active,
.btn-outline--deep-purple:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--indigo {
  --color: #3f51b5;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--indigo:hover,
.btn--indigo:active,
.btn--indigo:focus,
.btn--indigo:focus-visible {
  --color: #32408f;
}

.btn-outline--indigo {
  --color: #3f51b5;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--indigo:hover,
.btn-outline--indigo:focus,
.btn-outline--indigo:active,
.btn-outline--indigo:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--blue {
  --color: #2196f3;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--blue:hover,
.btn--blue:active,
.btn--blue:focus,
.btn--blue:focus-visible {
  --color: #0c7cd5;
}

.btn-outline--blue {
  --color: #2196f3;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--blue:hover,
.btn-outline--blue:focus,
.btn-outline--blue:active,
.btn-outline--blue:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--light-blue {
  --color: #45c5ff;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--light-blue:hover,
.btn--light-blue:active,
.btn--light-blue:focus,
.btn--light-blue:focus-visible {
  --color: #12b5ff;
}

.btn-outline--light-blue {
  --color: #45c5ff;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--light-blue:hover,
.btn-outline--light-blue:focus,
.btn-outline--light-blue:active,
.btn-outline--light-blue:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--cyan {
  --color: #00bcd4;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--cyan:hover,
.btn--cyan:active,
.btn--cyan:focus,
.btn--cyan:focus-visible {
  --color: #008fa1;
}

.btn-outline--cyan {
  --color: #00bcd4;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--cyan:hover,
.btn-outline--cyan:focus,
.btn-outline--cyan:active,
.btn-outline--cyan:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--teal {
  --color: #009688;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--teal:hover,
.btn--teal:active,
.btn--teal:focus,
.btn--teal:focus-visible {
  --color: #00635a;
}

.btn-outline--teal {
  --color: #009688;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--teal:hover,
.btn-outline--teal:focus,
.btn-outline--teal:active,
.btn-outline--teal:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--green {
  --color: #4caf50;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--green:hover,
.btn--green:active,
.btn--green:focus,
.btn--green:focus-visible {
  --color: #3d8b40;
}

.btn-outline--green {
  --color: #4caf50;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--green:hover,
.btn-outline--green:focus,
.btn-outline--green:active,
.btn-outline--green:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--light-green {
  --color: #8bc34a;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--light-green:hover,
.btn--light-green:active,
.btn--light-green:focus,
.btn--light-green:focus-visible {
  --color: #71a436;
}

.btn-outline--light-green {
  --color: #8bc34a;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--light-green:hover,
.btn-outline--light-green:focus,
.btn-outline--light-green:active,
.btn-outline--light-green:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--lime {
  --color: #cddc39;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--lime:hover,
.btn--lime:active,
.btn--lime:focus,
.btn--lime:focus-visible {
  --color: #b2c022;
}

.btn-outline--lime {
  --color: #cddc39;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--lime:hover,
.btn-outline--lime:focus,
.btn-outline--lime:active,
.btn-outline--lime:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--yellow {
  --color: #ffeb3b;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--yellow:hover,
.btn--yellow:active,
.btn--yellow:focus,
.btn--yellow:focus-visible {
  --color: #ffe608;
}

.btn-outline--yellow {
  --color: #ffeb3b;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--yellow:hover,
.btn-outline--yellow:focus,
.btn-outline--yellow:active,
.btn-outline--yellow:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--amber {
  --color: #f2b809;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--amber:hover,
.btn--amber:active,
.btn--amber:focus,
.btn--amber:focus-visible {
  --color: #c19307;
}

.btn-outline--amber {
  --color: #f2b809;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--amber:hover,
.btn-outline--amber:focus,
.btn-outline--amber:active,
.btn-outline--amber:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--orange {
  --color: #ff9800;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--orange:hover,
.btn--orange:active,
.btn--orange:focus,
.btn--orange:focus-visible {
  --color: #cc7a00;
}

.btn-outline--orange {
  --color: #ff9800;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--orange:hover,
.btn-outline--orange:focus,
.btn-outline--orange:active,
.btn-outline--orange:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--brown {
  --color: #795548;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--brown:hover,
.btn--brown:active,
.btn--brown:focus,
.btn--brown:focus-visible {
  --color: #593f35;
}

.btn-outline--brown {
  --color: #795548;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--brown:hover,
.btn-outline--brown:focus,
.btn-outline--brown:active,
.btn-outline--brown:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--1 {
  --color: #127681;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--1:hover,
.btn--1:active,
.btn--1:focus,
.btn--1:focus-visible {
  --color: #0c4d54;
}

.btn-outline--1 {
  --color: #127681;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--1:hover,
.btn-outline--1:focus,
.btn-outline--1:active,
.btn-outline--1:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--2 {
  --color: #ea907a;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--2:hover,
.btn--2:active,
.btn--2:focus,
.btn--2:focus-visible {
  --color: #e36b4e;
}

.btn-outline--2 {
  --color: #ea907a;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--2:hover,
.btn-outline--2:focus,
.btn-outline--2:active,
.btn-outline--2:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--3 {
  --color: #10375c;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--3:hover,
.btn--3:active,
.btn--3:focus,
.btn--3:focus-visible {
  --color: #081d31;
}

.btn-outline--3 {
  --color: #10375c;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--3:hover,
.btn-outline--3:focus,
.btn-outline--3:active,
.btn-outline--3:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--4 {
  --color: #4f8a8b;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--4:hover,
.btn--4:active,
.btn--4:focus,
.btn--4:focus-visible {
  --color: #3d6a6a;
}

.btn-outline--4 {
  --color: #4f8a8b;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--4:hover,
.btn-outline--4:focus,
.btn-outline--4:active,
.btn-outline--4:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--5 {
  --color: #d92027;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--5:hover,
.btn--5:active,
.btn--5:focus,
.btn--5:focus-visible {
  --color: #ad191f;
}

.btn-outline--5 {
  --color: #d92027;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--5:hover,
.btn-outline--5:focus,
.btn-outline--5:active,
.btn-outline--5:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--6 {
  --color: #ff9234;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--6:hover,
.btn--6:active,
.btn--6:focus,
.btn--6:focus-visible {
  --color: #ff7701;
}

.btn-outline--6 {
  --color: #ff9234;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--6:hover,
.btn-outline--6:focus,
.btn-outline--6:active,
.btn-outline--6:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--7 {
  --color: #4cd3c2;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--7:hover,
.btn--7:active,
.btn--7:focus,
.btn--7:focus-visible {
  --color: #2fbdab;
}

.btn-outline--7 {
  --color: #4cd3c2;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--7:hover,
.btn-outline--7:focus,
.btn-outline--7:active,
.btn-outline--7:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--8 {
  --color: #35d0ba;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--8:hover,
.btn--8:active,
.btn--8:focus,
.btn--8:focus-visible {
  --color: #28aa98;
}

.btn-outline--8 {
  --color: #35d0ba;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--8:hover,
.btn-outline--8:focus,
.btn-outline--8:active,
.btn-outline--8:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--9 {
  --color: #e84a5f;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--9:hover,
.btn--9:active,
.btn--9:focus,
.btn--9:focus-visible {
  --color: #e21d37;
}

.btn-outline--9 {
  --color: #e84a5f;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--9:hover,
.btn-outline--9:focus,
.btn-outline--9:active,
.btn-outline--9:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--10 {
  --color: #00005c;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--10:hover,
.btn--10:active,
.btn--10:focus,
.btn--10:focus-visible {
  --color: #000029;
}

.btn-outline--10 {
  --color: #00005c;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--10:hover,
.btn-outline--10:focus,
.btn-outline--10:active,
.btn-outline--10:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--11 {
  --color: #45046a;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--11:hover,
.btn--11:active,
.btn--11:focus,
.btn--11:focus-visible {
  --color: #250239;
}

.btn-outline--11 {
  --color: #45046a;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--11:hover,
.btn-outline--11:focus,
.btn-outline--11:active,
.btn-outline--11:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--12 {
  --color: #5c2a9d;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--12:hover,
.btn--12:active,
.btn--12:focus,
.btn--12:focus-visible {
  --color: #441f75;
}

.btn-outline--12 {
  --color: #5c2a9d;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--12:hover,
.btn-outline--12:focus,
.btn-outline--12:active,
.btn-outline--12:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--13 {
  --color: #6a097d;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--13:hover,
.btn--13:active,
.btn--13:focus,
.btn--13:focus-visible {
  --color: #42064d;
}

.btn-outline--13 {
  --color: #6a097d;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--13:hover,
.btn-outline--13:focus,
.btn-outline--13:active,
.btn-outline--13:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--14 {
  --color: #ff5200;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--14:hover,
.btn--14:active,
.btn--14:focus,
.btn--14:focus-visible {
  --color: #cc4200;
}

.btn-outline--14 {
  --color: #ff5200;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--14:hover,
.btn-outline--14:focus,
.btn-outline--14:active,
.btn-outline--14:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--15 {
  --color: #162447;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--15:hover,
.btn--15:active,
.btn--15:focus,
.btn--15:focus-visible {
  --color: #0a1020;
}

.btn-outline--15 {
  --color: #162447;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--15:hover,
.btn-outline--15:focus,
.btn-outline--15:active,
.btn-outline--15:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--16 {
  --color: #e43f5a;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--16:hover,
.btn--16:active,
.btn--16:focus,
.btn--16:focus-visible {
  --color: #d21e3b;
}

.btn-outline--16 {
  --color: #e43f5a;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--16:hover,
.btn-outline--16:focus,
.btn-outline--16:active,
.btn-outline--16:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--17 {
  --color: #035aa6;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--17:hover,
.btn--17:active,
.btn--17:focus,
.btn--17:focus-visible {
  --color: #023f74;
}

.btn-outline--17 {
  --color: #035aa6;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--17:hover,
.btn-outline--17:focus,
.btn-outline--17:active,
.btn-outline--17:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--18 {
  --color: #0779e4;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--18:hover,
.btn--18:active,
.btn--18:focus,
.btn--18:focus-visible {
  --color: #055fb3;
}

.btn-outline--18 {
  --color: #0779e4;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--18:hover,
.btn-outline--18:focus,
.btn-outline--18:active,
.btn-outline--18:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--19 {
  --color: #342ead;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--19:hover,
.btn--19:active,
.btn--19:focus,
.btn--19:focus-visible {
  --color: #282385;
}

.btn-outline--19 {
  --color: #342ead;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--19:hover,
.btn-outline--19:focus,
.btn-outline--19:active,
.btn-outline--19:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--20 {
  --color: #d7385e;
  background-color: var(--color) !important;
  border: 1px solid var(--color) !important;
  color: var(--btn-color) !important;
}

.btn--20:hover,
.btn--20:active,
.btn--20:focus,
.btn--20:focus-visible {
  --color: #b72548;
}

.btn-outline--20 {
  --color: #d7385e;
  color: var(--color) !important;
  border-color: var(--color) !important;
}

.btn-outline--20:hover,
.btn-outline--20:focus,
.btn-outline--20:active,
.btn-outline--20:focus-visible {
  background-color: var(--color) !important;
  color: var(--btn-color) !important;
}

/* btn shadow css start */
.btn--shadow-default {
  box-shadow: 0 8px 15px 0 rgba(0, 0, 0, 0.2) !important;
}

.btn--primary.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(115, 103, 240, 0.35);
}

.btn--primary.btn--shadow:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px 0 rgba(115, 103, 240, 0.45);
}

.btn--secondary.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(134, 142, 150, 0.35);
}

.btn--secondary.btn--shadow:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px 0 rgba(134, 142, 150, 0.45);
}

.btn--success.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(40, 199, 111, 0.35);
}

.btn--success.btn--shadow:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px 0 rgba(40, 199, 111, 0.45);
}

.btn--danger.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(234, 84, 85, 0.35);
}

.btn--danger.btn--shadow:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px 0 rgba(234, 84, 85, 0.45);
}

.btn--warning.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(255, 159, 67, 0.35);
}

.btn--warning.btn--shadow:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px 0 rgba(255, 159, 67, 0.45);
}

.btn--info.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(30, 159, 242, 0.35);
}

.btn--info.btn--shadow:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px 0 rgba(30, 159, 242, 0.45);
}

.btn--dark.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(16, 22, 58, 0.35);
}

.btn--dark.btn--shadow:hover {
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px 0 rgba(16, 22, 58, 0.45);
}

.btn-outline--primary.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(115, 103, 240, 0.35);
}

.btn-outline--primary.btn--shadow:hover {
  box-shadow: 0 8px 15px 0 rgba(115, 103, 240, 0.45);
}

.btn-outline--secondary.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(134, 142, 150, 0.35);
}

.btn-outline--secondary.btn--shadow:hover {
  box-shadow: 0 8px 15px 0 rgba(134, 142, 150, 0.45);
}

.btn-outline--success.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(40, 199, 111, 0.35);
}

.btn-outline--success.btn--shadow:hover {
  box-shadow: 0 8px 15px 0 rgba(40, 199, 111, 0.45);
}

.btn-outline--danger.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(234, 84, 85, 0.35);
}

.btn-outline--danger.btn--shadow:hover {
  box-shadow: 0 8px 15px 0 rgba(234, 84, 85, 0.45);
}

.btn-outline--warning.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(255, 159, 67, 0.35);
}

.btn-outline--warning.btn--shadow:hover {
  box-shadow: 0 8px 15px 0 rgba(255, 159, 67, 0.45);
}

.btn-outline--info.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(30, 159, 242, 0.35);
}

.btn-outline--info.btn--shadow:hover {
  box-shadow: 0 8px 15px 0 rgba(30, 159, 242, 0.45);
}

.btn-outline--dark.btn--shadow {
  box-shadow: 0 5px 10px 0 rgba(16, 22, 58, 0.35);
}

.btn-outline--dark.btn--shadow:hover {
  box-shadow: 0 8px 15px 0 rgba(16, 22, 58, 0.45);
}

/* btn shadow css end */

/* btn gradient css start */
.btn--gradi {
  border: none;
  text-shadow: 1px 2px 4px rgba(0, 0, 0, 0.3);
}

.btn--primary.btn--gradi,
.btn-primary.btn--gradi {
  background: #640064;
  background-image: linear-gradient(30deg, #4634ff, rgba(115, 103, 240, 0.5)) !important;
  text-shadow: 1px 2px 4px rgba(0, 0, 0, 0.3);
}

.btn--secondary.btn--gradi,
.btn-secondary.btn--gradi {
  background: linear-gradient(to right, #636f8e 0%, #acb8da 100%);
}

.btn--success.btn--gradi,
.btn-success.btn--gradi {
  background: #0064fa;
  background-image: linear-gradient(30deg, #28c76f, rgba(40, 199, 111, 0.6)) !important;
}

.btn--danger.btn--gradi,
.btn-danger.btn--gradi {
  background: #640064;
  background-image: linear-gradient(30deg, #eb2222, rgba(234, 84, 85, 0.15)) !important;
}

.btn--warning.btn--gradi,
.btn-warning.btn--gradi {
  background: #fafafa;
  background-image: linear-gradient(30deg, #ff9f43, rgba(255, 159, 67, 0.5)) !important;
}

.btn--info.btn--gradi,
.btn-info.btn--gradi {
  background: linear-gradient(30deg, #4eb4f5 0%, #0b75b9 100%) !important;
}

.btn--dark.btn--gradi,
.btn-dark.btn--gradi {
  background: linear-gradient(30deg, #7180d8 0%, black 100%) !important;
}

/* btn shadow css end */
/* btn css end */

/* card css start */
.open-code-btn {
  background-color: #000000;
  color: #ffffff;
  font-size: 0.75rem;
  padding: 5px 10px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.2);
}

.open-code-btn:hover {
  color: #ffffff;
}

.card {
  border: none;
  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.card.text-white .card-title,
.card.text-white .card-text {
  color: #ffffff;
}

.card .card-img.style--horizontal {
  height: 100%;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  -o-object-position: center;
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(140, 140, 140, 0.125);
}

.text-white p {
  color: #ffffff;
}

.code-body {
  margin-top: 30px;
  box-shadow: 0 5px 25px 0 rgba(0, 0, 0, 0.1);
}

.code-body .card-body {
  background-color: #2d2d2d;
  padding: 0;
}

.card-img-overlay {
  z-index: 1;
}

.card-img-overlay::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  opacity: 0.5;
  z-index: -1;
}

.card-img {
  height: 100%;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  -o-object-position: center;
}

.card-img-overlay {
  overflow: hidden;
}

.card-footer {
  background-color: #ffffff;
  border-top: 1px solid #e8e8e8;
}

.alert {
  display: flex;
  align-items: center;
  padding: 0;
  border: none;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  overflow: hidden;
  align-items: stretch;
}

.alert button.close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 12px;
}

.alert__message {
  padding: 12px;
  padding-right: 22px;
}

.alert__icon {
  padding: 13px 14px;
  background-color: rgba(0, 0, 0, 0.1);
}

/* card css end */
/* form css start */
label {
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.form-group {
  margin-bottom: 15px;
}

input:not([type='radio']),
textarea {
  padding: 10px 20px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background-color: transparent;
  font-size: 0.875rem !important;
}

input:not([type='radio'])::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #c9c9c9 !important;
}

input:not([type='radio'])::-moz-placeholder,
textarea::-moz-placeholder {
  color: #c9c9c9 !important;
}

input:not([type='radio']):-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #c9c9c9 !important;
}

input:not([type='radio']):-moz-placeholder,
textarea:-moz-placeholder {
  color: #c9c9c9 !important;
}

select {
  padding: 8px 10px;
  cursor: pointer;
  color: #5b6e88;
  background-color: transparent;
  border-color: #ced4da;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

textarea {
  min-height: 100px;
  width: 100%;
  border-color: #ced4da;
}

textarea.resize--none {
  resize: none;
}

input[type='radio'],
input[type='range'],
input[type='checkbox'] {
  padding: 0;
}

input.form-control,
select.form-control {
  height: 45px;
}

.form-control[readonly],
.form-control[disabled] {
  background-color: #8f8f8f54;
  pointer-events: none;
}

.input-group .btn {
  border-radius: 5px;
}

.form-inline .input-group .form-control {
  flex: 0 0 auto;
  width: auto;
}

.h-45 {
  height: 45px;
}

a.btn.h-45 {
  line-height: 27.5px;
}

select.form-control {
  -webkit-appearance: auto;
  -moz-appearance: auto;
  appearance: auto;
}

input.form-control-xl {
  height: calc(2rem + 1rem + 2px);
  font-size: 1.125rem !important;
}

input.form-control-lg {
  height: calc(1.8rem + 1rem + 2px);
  font-size: 1rem !important;
}

input.form-control-sm {
  height: calc(1rem + 1rem + 2px);
  font-size: 0.8125rem !important;
}

.label--text {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.form-control:focus,
.form-control:active,
.form-control:visited,
.form-control:focus-within,
input:focus,
input:active,
input:visited,
input:focus-within,
textarea:focus,
textarea:active,
textarea:visited,
textarea:focus-within,
select:focus,
select:active,
select:visited,
select:focus-within {
  border-color: #4634ff;
  box-shadow: 0 3px 9px rgba(50, 50, 9, 0.05), 3px 4px 8px rgba(115, 103, 240, 0.1);
}

.toggle.btn {
  height: 45px !important;
  border-radius: 5px;
}

.toggle.btn:active {
  border-color: transparent;
}

.toggle-on.btn,
.toggle-off.btn {
  line-height: 32px;
}

.toggle-group .btn {
  border-radius: 5px;
}

.toggle-group .btn:active {
  color: #fff;
}

.custom-control-label::before,
.custom-control-label::after {
  top: 0;
}

.form-check-primary .custom-control-input:checked~.custom-control-label::before {
  border-color: #4634ff;
  background-color: #4634ff;
}

.form-check-secondary .custom-control-input:checked~.custom-control-label::before {
  border-color: #868e96;
  background-color: #868e96;
}

.form-check-success .custom-control-input:checked~.custom-control-label::before {
  border-color: #28c76f;
  background-color: #28c76f;
}

.form-check-info .custom-control-input:checked~.custom-control-label::before {
  border-color: #1e9ff2;
  background-color: #1e9ff2;
}

.form-check-warning .custom-control-input:checked~.custom-control-label::before {
  border-color: #ff9f43;
  background-color: #ff9f43;
}

.form-check-danger .custom-control-input:checked~.custom-control-label::before {
  border-color: #eb2222;
  background-color: #eb2222;
}

.toggle-group .toggle-handle {
  background-color: #10163a;
}

.toggle-group .toggle-off {
  background-color: #293654;
  color: #ffffff;
}

.toggle input[data-size='small']~.toggle-group label {
  font-size: 0.75rem;
}

.timepicki.time_pick input {
  width: 100%;
  border: 1px solid #ced4da;
}

.timepicki .timepicker_wrap {
  margin-bottom: 30px;
  background-color: #ffffff;
  box-shadow: 0px 5px 20px 0 rgba(123, 123, 123, 0.25);
}

.timepicki .timepicker_wrap .prev,
.timepicki .timepicker_wrap .next {
  width: 58px;
  padding: 17px;
}

.timepicki .timepicker_wrap .timepicki-input {
  padding: 15px 10px;
  font-size: 16px !important;
  font-weight: 700;
}

.pincode-input-container input:focus,
.pincode-input-container input:active {
  box-shadow: none;
  border-color: #4634ff;
}

.pincode-input-container input~input:focus,
.pincode-input-container input~input:active {
  border-left-width: 1px;
}

.custom-switch .custom-control-label::after {
  top: calc(0.25rem + -2px);
}

.timepicker_wrap .action-next,
.timepicker_wrap .action-prev {
  position: relative;
}

.timepicker_wrap .action-next::before,
.timepicker_wrap .action-prev::before {
  position: absolute;
  content: '\f107';
  font-family: 'Font Awesome 5 Free';
  color: #000000;
  left: 23px;
}

.timepicker_wrap .action-next::before {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
  top: 0;
}

.timepicker_wrap .action-prev::before {
  bottom: 0;
}

/* form css end */
/* table css start */
.table {
  margin-bottom: 0;
}

.table> :not(:first-child) {
  border-top: none;
}

.table th {
  font-size: 0.75rem;
  text-align: center;
  padding: 15px 25px;
  white-space: nowrap;
}

.table td {
  font-size: 0.8125rem;
  color: #5b6e88;
  text-align: center;
  font-weight: 500;
  padding: 15px 25px;
  vertical-align: middle;
  white-space: nowrap;
}

.white-space-wrap {
  white-space: initial !important;
}

.table td,
.table th {
  border-top: 1px solid #e8e8e8;
}

.table tbody tr:last-child td {
  border-bottom: none;
}

.table td span,
.table td p,
.table td li {
  font-size: 0.875rem;
}

table th:last-child {
  text-align: right;
}

table th:first-child {
  text-align: left;
  font-weight: 600;
}

table td:last-child {
  text-align: right;
}

table td:first-child {
  text-align: left;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(115, 115, 115, 0.05);
}

table .user {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

table .user .thumb {
  width: 40px;
  height: 40px;
}

table .user .thumb img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  -o-object-position: center;
  border: 2px solid #ffffff;
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.2);
}

table .user .name {
  width: calc(100% - 40px);
  padding-left: 10px;
}

@media (max-width: 575px) {
  .table td {
    white-space: initial;
  }
}

div.dataTables_wrapper div.dataTables_filter {
  display: inline-block;
  float: right;
}

@media (max-width: 767px) {
  div.dataTables_wrapper div.dataTables_filter {
    float: none;
    width: 100%;
    margin-top: 20px;
  }
}

div.dataTables_wrapper div.dataTables_filter input:focus {
  box-shadow: none;
  border-color: #4634ff;
}

div.dataTables_wrapper div.dataTables_filter label {
  color: #5b6e88;
}

div.dataTables_wrapper .dt-buttons button {
  border-radius: 3px !important;
  margin: 0 10px;
  background-color: transparent;
  border: 1px solid #d8d8d8;
  border-radius: 5px !important;
  -webkit-border-radius: 5px !important;
  -moz-border-radius: 5px !important;
  -ms-border-radius: 5px !important;
  -o-border-radius: 5px !important;
}

div.dataTables_wrapper .dt-buttons button:hover,
div.dataTables_wrapper .dt-buttons button:focus,
div.dataTables_wrapper .dt-buttons button:active {
  background-color: #4634ff !important;
  border-color: #4634ff !important;
  box-shadow: 0px 0px 7px 2px rgba(115, 103, 240, 0.46) !important;
}

div.dataTables_wrapper .dt-buttons button:hover span,
div.dataTables_wrapper .dt-buttons button:focus span,
div.dataTables_wrapper .dt-buttons button:active span {
  color: #ffffff;
}

div.dataTables_wrapper .dataTables_info {
  float: left;
  color: #5b6e88;
  font-size: 0.875rem;
}

@media (max-width: 767px) {
  div.dataTables_wrapper .dataTables_info {
    float: none;
  }
}

div.dataTables_wrapper .dataTables_paginate {
  float: right;
}

@media (max-width: 767px) {
  div.dataTables_wrapper .dataTables_paginate {
    float: none;
    margin-top: 15px !important;
  }
}

div.dataTables_wrapper .dataTables_length label {
  color: #5b6e88;
}

div.dataTables_wrapper .dataTables_length label select {
  margin-left: 10px;
  margin-right: 10px;
}

table.dataTable {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

table.dataTable tbody tr:first-child td {
  border-top: 1px solid #e5e5e5;
}

table.dataTable tbody tr:last-child td {
  border-bottom: 1px solid #e5e5e5;
}

table.dataTable tbody tr td {
  white-space: nowrap;
}

table.dataTable thead tr {
  background-color: #4634ff;
}

table.dataTable thead tr th {
  border: none;
  color: #ffffff;
  white-space: nowrap;
}

.pagination {
  flex-wrap: wrap;
  margin: -3px -4px;
}

.pagination li {
  margin: 3px 4px;
}

.dataTables_paginate .pagination {
  flex-wrap: wrap;
  margin: -5px -7px;
}

.dataTables_paginate .pagination .page-item {
  margin: 5px 7px;
}

.dataTables_paginate .pagination .page-item.active .page-link {
  background-color: #4634ff;
  border-color: #4634ff;
  box-shadow: 0px 0px 5px 2px rgba(115, 103, 240, 0.46);
}

.dataTables_paginate .pagination .page-item .page-link {
  font-size: 0.875rem;
  color: #5b6e88;
  border-radius: 5px !important;
  -webkit-border-radius: 5px !important;
  -moz-border-radius: 5px !important;
  -ms-border-radius: 5px !important;
  -o-border-radius: 5px !important;
  width: auto;
  height: auto;
  padding: 8px 15px;
}

.dataTables_paginate .pagination li a:focus {
  box-shadow: 0px 0px 7px 2px rgba(115, 103, 240, 0.46);
}

table thead th:first-child {
  border-radius: 5px 0 0 0;
}

table thead th:last-child {
  border-radius: 0 5px 0 0;
}

table.table--light thead th {
  border: none;
  color: #ffffff;
  background-color: #4634ff;
}

table.table--light.style--two thead th {
  border-top: none;
  padding-left: 25px;
  padding-right: 25px;
}

table.table--light.style--two tbody td {
  padding: 15px 25px;
}

.customer-details {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
}

.customer-details .thumb {
  width: 45px;
  height: 45px;
}

.customer-details .thumb img {
  width: 45px;
  height: 45px;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  -o-object-position: center;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.customer-details .content {
  padding-left: 15px;
}

.user-table-list {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: center;
}

.user-table-list .user+.user {
  margin-left: -10px;
  z-index: 1;
}

.user-table-list .user {
  width: 32px;
  height: 32px;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
  z-index: 2;
}

.user-table-list .user:hover {
  z-index: 2;
  -webkit-transform: scale(1.05, 1.05);
  -ms-transform: scale(1.05, 1.05);
  transform: scale(1.05, 1.05);
}

.user-table-list .user img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  -o-object-position: center;
  border: 2px solid #ffffff;
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.15);
}

.table> :not(caption)>*>* {
  border-bottom-width: 0;
}

[data-label] {
  position: relative;
}

[data-label]::before {
  position: absolute;
  content: attr(data-label);
  font-weight: 700;
  color: #000000;
  top: 0;
  left: 0;
  padding: 13px 15px;
  display: none;
}

.table-responsive--lg tbody tr:first-child td {
  border-top: none;
}

@media (max-width: 767px) {
  .table-responsive--sm table .user {
    justify-content: flex-end;
  }

  .table-responsive--sm table .user .name {
    width: auto;
  }

  table thead th:first-child {
    border-radius: 0;
  }

  table thead th:last-child {
    border-radius: 0;
  }

  table.dataTable .user {
    justify-content: flex-start;
  }

  table.dataTable .user .name {
    width: calc(100% - 40px);
  }

  .table-responsive--sm table.dataTable .user {
    justify-content: flex-end;
  }

  .table-responsive--sm table.dataTable .user .name {
    width: auto;
  }
}

@media (max-width: 1199px) {
  .table-responsive--lg thead {
    display: none;
  }

  table thead th:first-child {
    border-radius: 0;
  }

  table thead th:last-child {
    border-radius: 0;
  }

  .table-responsive--lg tbody tr:nth-child(odd) {
    background-color: #e2e2e233;
  }

  .table-responsive--lg tbody tr:nth-child(odd) td {
    border-top: 1px solid #10163a17;
  }

  .table-responsive--lg tr th,
  .table-responsive--lg tr td {
    display: block;
    padding-left: 45% !important;
    text-align: right !important;
  }

  .table-responsive--lg .user-table-list,
  .table-responsive--lg .customer-details {
    justify-content: flex-end;
  }

  .table-responsive--lg [data-label]::before {
    display: block;
  }

  .table-responsive--lg table.dataTable .user {
    justify-content: flex-end;
  }

  .table-responsive--lg table.dataTable .user .name {
    width: auto;
  }

  .table-responsive--lg table .user {
    justify-content: flex-end;
  }

  .table-responsive--lg table .user .name {
    width: auto;
  }
}

@media (max-width: 991px) {
  .table-responsive--md thead {
    display: none;
  }

  table thead th:first-child {
    border-radius: 0;
  }

  table thead th:last-child {
    border-radius: 0;
  }

  .table-responsive--md tbody tr:nth-child(odd) {
    background-color: #e2e2e233;
  }

  .table-responsive--md tbody tr:nth-child(odd) td {
    border-top: 1px solid #10163a17;
  }

  .table-responsive--md tbody tr:first-child td:first-child {
    border-top: none;
  }

  .table-responsive--md tr th,
  .table-responsive--md tr td {
    display: block;
    padding-left: 45% !important;
    text-align: right !important;
  }

  .table-responsive tr td.text-center {
    text-align: center !important;
    padding-left: 0 !important;
  }

  .table-responsive--md .user-table-list,
  .table-responsive--md .customer-details {
    justify-content: flex-end;
  }

  .table-responsive--md [data-label]::before {
    display: block;
  }

  .table-responsive--md table.dataTable .user {
    justify-content: flex-end;
  }

  .table-responsive--md table.dataTable .user .name {
    width: auto;
  }

  .table-responsive--md table .user {
    justify-content: flex-end;
  }

  .table-responsive--md table .user .name {
    width: auto;
  }
}

@media (max-width: 767px) {
  .table-responsive--sm thead {
    display: none;
  }

  table thead th:first-child {
    border-radius: 0;
  }

  table thead th:last-child {
    border-radius: 0;
  }

  .table-responsive--sm tbody tr:nth-child(odd) {
    background-color: #e2e2e233;
  }

  .table-responsive--sm tbody tr:nth-child(odd) td {
    border-top: 1px solid #10163a17;
  }

  .table-responsive--sm tbody tr:first-child td:first-child {
    border-top: none;
  }

  .table-responsive--sm tr th,
  .table-responsive--sm tr td {
    display: block;
    padding-left: 45% !important;
    text-align: right !important;
  }

  .table-responsive--sm .user-table-list,
  .table-responsive--sm .customer-details {
    justify-content: flex-end;
  }

  .table-responsive--sm [data-label]::before {
    display: block;
  }
}

@media (max-width: 575px) {
  .table-responsive--xs thead {
    display: none;
  }

  table thead th:first-child {
    border-radius: 0;
  }

  table thead th:last-child {
    border-radius: 0;
  }

  .table-responsive--xs tbody tr:nth-child(odd) {
    background-color: #e2e2e233;
  }

  .table-responsive--xs tr th,
  .table-responsive--xs tr td {
    display: block;
    padding-left: 45% !important;
    text-align: right !important;
  }

  .table-responsive--xs .user-table-list,
  .table-responsive--xs .customer-details {
    justify-content: flex-end;
  }

  .table-responsive--xs [data-label]::before {
    display: block;
  }
}

@media (max-width: 1199px) {

  *[class*='table-responsive--'].data-label--none tr th,
  *[class*='table-responsive--'].data-label--none tr td {
    padding-left: 0.75rem;
  }
}

*[class*='table-responsive--'] .table-dark tbody [data-label]::before {
  color: #5b6e88;
}

@media (max-width: 1199px) {
  .table-responsive--lg .table-dark tbody tr:nth-child(odd) {
    background-color: #343a40;
  }

  .table-responsive--lg .table-dark tbody tr:nth-child(even) {
    background-color: #222930;
  }

  .table-responsive--lg table.dataTable tbody tr td {
    white-space: normal;
  }
}

@media (max-width: 991px) {
  .table-responsive--md .table-dark tbody tr:nth-child(odd) {
    background-color: #343a40;
  }

  .table-responsive--md .table-dark tbody tr:nth-child(even) {
    background-color: #222930;
  }

  .table-responsive--md table.dataTable tbody tr td {
    white-space: normal;
  }
}

@media (max-width: 767px) {
  .table-responsive--sm .table-dark tbody tr:nth-child(odd) {
    background-color: #343a40;
  }

  .table-responsive--sm .table-dark tbody tr:nth-child(even) {
    background-color: #222930;
  }

  .table-responsive--sm table.dataTable tbody tr td {
    white-space: normal;
  }
}

@media (max-width: 575px) {
  .table-responsive--xs .table-dark tbody tr:nth-child(odd) {
    background-color: #343a40;
  }

  .table-responsive--xs .table-dark tbody tr:nth-child(even) {
    background-color: #222930;
  }

  ol.sec-item li i,
  ol.sec-item li span {
    font-size: 18px;
  }
}

.pagination .page-item.active .page-link {
  background-color: #4634ff;
  border-color: #4634ff;
  color: #ffffff;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.25);
}

.pagination .page-item.previous .page-link,
.pagination .page-item.next .page-link {
  font-size: 0;
  position: relative;
  width: 35px;
  height: 35px;
}

.pagination .page-item.previous .page-link::before {
  position: absolute;
  top: 7px;
  right: 11px;
  font-family: 'Font Awesome 5 Free';
  font-weight: 700;
  content: '\f104';
  font-size: 16px;
}

.pagination .page-item.next .page-link::before {
  position: absolute;
  top: 7px;
  right: 11px;
  font-family: 'Font Awesome 5 Free';
  font-weight: 700;
  content: '\f105';
  font-size: 16px;
}

.pagination .page-item .page-link,
.pagination .page-item span {
  font-size: 0.875rem;
  display: flex;
  width: 36px;
  height: 36px;
  margin: 0 3px;
  padding: 0;
  border-radius: 3px !important;
  align-items: center;
  justify-content: center;
  color: #5b6e88;
}

.page-link:focus {
  box-shadow: none;
}

@media (max-width: 420px) {
  div.dataTables_wrapper div.dataTables_filter input {
    width: 150px;
  }
}

th.w-85 {
  width: 85px;
}

/* table css end */
/* widget css start */
.has--link {
  position: relative;
}

.has--link .item--link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.widget {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.widget:hover {
  -webkit-transform: scale(1.05, 1.05);
  -ms-transform: scale(1.05, 1.05);
  transform: scale(1.05, 1.05);
}

.widget__icon {
  width: 70px;
  height: 70px;
  align-self: flex-start;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.widget__icon i {
  color: #ffffff;
  font-size: 38px;
}

.widget__content {
  width: calc(100% - 90px);
  padding-left: 15px;
}

.widget__content .stat-down {
  color: #eb2222;
}

.widget__content .stat-down i {
  font-size: 12px;
}

.widget__content .stat-down span {
  color: #5b6e88;
}

.widget__content .stat-up {
  color: #28c76f;
}

.widget__content .stat-up i {
  font-size: 12px;
}

.widget__content .stat-up span {
  color: #5b6e88;
}

.widget__arrow {
  width: 20px;
  text-align: right;
  color: #5b6e88;
}

.widget-two {
  padding: 15px 15px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  position: relative;
  overflow: hidden;
  align-items: center;
  height: 100%;
}

.widget-two .overlay-icon {
  position: absolute;
  bottom: -15px;
  right: -15px;
  font-size: 70px;
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  opacity: 0.15;
}

.widget-two__icon {
  width: 65px;
  height: 65px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.widget-two__icon i {
  font-size: 42px;
}

.widget-two__content {
  width: calc(100% - 65px);
  padding-left: 20px;
}

.widget-two__btn {
  position: absolute;
  top: 5px;
  right: 5px;
  border-radius: 4px;
  font-size: 10px;
  padding: 0 5px;
  transition: all 0.3s;
}

.widget-two.style--two {
  z-index: 1;
}

.widget-two.style--two::before,
.widget-two.style--two::after {
  position: absolute;
  content: '';
  top: 0;
  right: 0;
  width: 20%;
  height: 100%;
  background-color: #fff;
  clip-path: polygon(40% 0, 100% 0, 100% 100%, 0 100%);
  -webkit-clip-path: polygon(40% 0, 100% 0, 100% 100%, 0 100%);
  opacity: 0.05;
  z-index: -1;
}

.widget-two.style--two::after {
  width: calc(20% + 20px);
}

.widget-two.style--two .widget-two__icon {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.widget-two.style--two .widget-two__btn {
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
  color: #fff;
  z-index: 1;
}

.widget-two.style--two .overlay-icon {
  opacity: 0;
}

.widget-three {
  padding: 30px 30px;
  text-align: center;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.widget-three:hover {
  -webkit-transform: scale(1.05, 1.05);
  -ms-transform: scale(1.05, 1.05);
  transform: scale(1.05, 1.05);
}

.widget-three__icon {
  width: 90px;
  height: 90px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  overflow: hidden;
}

.widget-three__icon i {
  font-size: 46px;
  color: #ffffff;
}

.widget-three__content {
  margin-top: 25px;
}

.widget-three__content .numbers {
  font-size: 24px;
  font-weight: 600;
}

.widget-four {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
}

.widget-four .widget__icon {
  width: 75px;
  height: 75px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  overflow: hidden;
}

.widget-four .widget__content {
  width: calc(100% - 75px);
  padding-left: 30px;
}

.widget-five {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
}

.widget-five .widget__icon {
  width: 65px;
  height: 65px;
  overflow: hidden;
}

.widget-five .widget__content {
  width: calc(100% - 65px);
  padding-left: 20px;
}

.widget-five .widget__content ul li+li {
  margin-top: 5px;
}

.widget-six .widget-six__top {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.widget-six .widget-six__top i {
  width: 40px;
  height: 40px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.widget-six .widget-six__top p {
  width: calc(100% - 40px);
  padding-left: 15px;
}

.widget-six .widget-six__bottom {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.widget-seven {
  border: 1px solid var(--color);
  border-radius: 5px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.04);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
  background-color: #fff !important;
  padding: 20px 18px;
  display: flex;
  gap: 5px;
  justify-content: space-between;
  height: 100%;
}

.widget-seven.outline {
  border-color: #fff;
}

.widget-seven .widget-seven__content {
  display: flex;
  gap: 16px;
  align-items: center;
}

.widget-seven .widget-seven__content-icon {
  width: 55px;
  height: 55px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.widget-seven .widget-seven__content-icon::after {
  content: '';
  position: absolute;
  inset: 0;
  background-color: var(--color);
  z-index: -1;
  opacity: 0.2;
  border-radius: 4px;
}

.widget-seven.outline .widget-seven__content-icon::after {
  background-color: #fff !important;
  border: 1px solid var(--color);
  opacity: 1;
}

.widget-seven .widget-seven__content-icon .icon {
  color: var(--color);
  font-size: 25px;
}

.widget-seven .widget-seven__arrow {
  align-self: center;
  color: #000;
  font-size: 14px;
  min-width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.widget-seven .widget-seven__arrow i {
  line-height: 1;
}

.widget-seven .widget-seven__content-title,
.widget-seven .widget-seven__content-subheading {
  font-size: 14px;
  font-weight: 500;
  color: #34495e;
}

.widget-seven .widget-seven__content-amount {
  color: #34495e;
  font-size: 27px;
  font-weight: 600;
}

@media screen and (max-width: 1440px) {
  .widget-seven .widget-seven__content-amount {
    font-size: 22px;
  }
}

@media screen and (max-width: 1024px) {
  .widget-seven {
    padding: 15px 18px;
  }
}

@media screen and (max-width: 767px) {
  .widget-seven .widget-seven__content-amount {
    font-size: 20px;
  }
}

@media screen and (max-width: 575px) {
  .widget-seven {
    padding: 10px 15px;
  }
}

.widget-eight {
  border-radius: 5px;
  background-color: transparent !important;
  padding: 20px 18px;
  display: flex;
  gap: 5px;
  justify-content: space-between;
  position: relative;
  z-index: 1;
  height: 100%;
}

.widget-eight.style-two {
  padding: 0;
  gap: 0px;
  height: 100%;
}

.widget-eight::after {
  position: absolute;
  content: '';
  inset: 0;
  border-radius: 5px;
  background-color: var(--color);
  opacity: 0.25;
  z-index: -1;
}

.widget-eight.style-two::after {
  background-color: unset;
}

.widget-eight .widget-eight__content-amount {
  color: var(--color);
  font-size: 27px;
  font-weight: 600;
}

.widget-eight.style-two .widget-eight__description {
  padding: 20px 18px 15px;
  position: relative;
  flex: 1;
  z-index: 1;
}

.widget-eight.style-two .widget-eight__description::after,
.widget-eight.style-two .widget-eight__description::before {
  position: absolute;
  content: '';
  inset: 0;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.widget-eight.style-two .widget-eight__description::after {
  background-color: var(--color);
  z-index: -2;
}

.widget-eight.style-two .widget-eight__description::before {
  z-index: -1;
  background-color: #000;
  opacity: 0.3;
  z-index: -1;
}

.widget-eight.style-two .widget-eight__content-amount,
.widget-eight.style-two .widget-eight__content-title {
  color: #fff;
}

.widget-eight .widget-eight__content-icon {
  align-self: center;
  font-size: 25px;
  flex-shrink: 0;
}

.widget-eight.style-two .widget-eight__content-icon {
  align-self: stretch;
  font-size: 25px;
  flex-shrink: 0;
  background-color: var(--color);
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.widget-eight .widget-eight__content-icon .icon {
  color: var(--color);
}

.widget-eight.style-two .widget-eight__content-icon .icon {
  color: #fff;
}

.widget-eight .widget-eight__content-title {
  font-size: 14px;
  font-weight: 500;
  color: #34495e;
}

@media screen and (max-width: 1440px) {
  .widget-eight .widget-eight__content-amount {
    font-size: 22px;
  }
}

@media screen and (max-width: 1024px) {
  .widget-eight {
    padding: 15px 18px;
  }
}

@media screen and (max-width: 767px) {
  .widget-eight .widget-eight__content-amount {
    font-size: 20px;
  }
}

@media screen and (max-width: 575px) {
  .widget-eight {
    padding: 10px 15px;
  }
}

.box-shadow3 {
  box-shadow: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a !important;
}

.widget-card-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0;
  width: 100%;
}

.widget-card {
  border: 0;
  width: calc(50% - 12px);
  border-bottom: 1px solid rgb(7 18 81 / 7%);
  border-left: 1px solid rgb(7 18 81 / 7%);
  border-radius: 0;
  width: calc(100% / 2);
  padding: 16px;
  position: relative;
}

.widget-card:hover {
  background-color: #f8f8f8 !important;
}

.widget-card .widget-card-link {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 9;
}

.widget-card .widget-card-arrow {
  height: 24px;
  width: 24px;
  display: grid;
  place-content: center;
  font-size: 16px;
  border-radius: 4px;
  border-radius: 50%;
  color: black;
  border: 0;
  flex-shrink: 0;
}

.widget-card:nth-of-type(3),
.widget-card:nth-of-type(4) {
  border-bottom: 0;
}

.widget-card:nth-of-type(1),
.widget-card:nth-of-type(3) {
  border-left: 0;
}

.widget-card-wrapper .widget-card-amount,
.widget-card-inner .widget-card-amount {
  font-size: 16px;
}

.widget-card-wrapper .widget-card-title,
.widget-card-inner .widget-card-title {
  font-size: 14px;
  font-weight: 300;
}

.widget-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  background-color: transparent !important;
}

.widget-card-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  flex: 1;
}

.widget-card-icon {
  flex-shrink: 0;
  height: 50px;
  width: 50px;
  font-size: 20px;
  display: grid;
  place-content: center;
  color: var(--color, #000);

  position: relative;
  z-index: 1;
}

.widget-card-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
  background-color: var(--color, #000);
  opacity: 0.2;
  border-radius: 6px;
}

.widget-card-amount {
  font-size: 14px;
  font-weight: 400;
  color: rgb(14, 23, 38);
  line-height: 1;
  margin-bottom: 6px;
}

.widget-card-title {
  color: rgb(136 142 168/ 1);
  display: block;
  line-height: 1;
  font-size: 12px;
}

@media (max-width: 767px) {
  .widget-card-icon {
    height: 40px;
    width: 40px;
    font-size: 18px;
  }

  .widget-card-wrapper .widget-card-amount,
  .widget-card-inner .widget-card-amount {
    font-size: 14px;
    font-weight: 500;
  }

  .widget-card-wrapper .widget-card-title,
  .widget-card-inner .widget-card-title {
    font-size: 12px;
    font-weight: 400;
  }

  .widget-card {
    gap: 8px;
  }
}

@media (max-width: 575px) {
  .widget-card {
    padding: 14px 12px;
  }
}

@media (max-width: 425px) {
  .widget-card {
    width: 100%;
    border: 0;
  }

  .widget-card:not(:last-child) {
    border-bottom: 1px solid rgb(7 18 81 / 7%);
  }
}

.widget-card-inner {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.widget-card-inner .widget-card {
  border: 0;
  flex: 1 1 auto;
}

.widget-card-inner .widget-card:not(:last-child) {
  border-right: 1px solid rgb(7 18 81 / 7%);
}

@media (max-width: 1199px) {
  .widget-card-inner {
    flex-wrap: wrap;
  }

  .widget-card-inner .widget-card {
    width: calc(100% / 2);
  }

  .widget-card-inner .widget-card:nth-child(2) {
    border-right: 0;
  }

  .widget-card-inner .widget-card:nth-child(1),
  .widget-card-inner .widget-card:nth-child(2) {
    border-bottom: 1px solid rgb(7 18 81 / 7%);
  }
}

@media (max-width: 425px) {
  .widget-card-inner .widget-card {
    border: 0 !important;
    width: 100%;
  }

  .widget-card-inner .widget-card:not(:last-child) {
    border-bottom: 1px solid rgb(7 18 81 / 7%) !important;
  }
}

.has-link {
  position: relative;
}

.item-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9;
}

.dashboard-w1 {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  min-height: 130px;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  position: relative;
  align-items: center;
  padding: 30px 20px;
}

.dashboard-w1:hover {
  transform: translateY(-3px);
  -webkit-transform: translateY(-3px);
  -moz-transform: translateY(-3px);
  -ms-transform: translateY(-3px);
  -o-transform: translateY(-3px);
}

.dashboard-w1 .icon {
  position: absolute;
  bottom: 0;
  left: 0;
}

.dashboard-w1 .icon i {
  font-size: 72px;
  color: rgba(255, 255, 255, 0.15);
  margin-left: -15px;
  margin-bottom: -4px;
}

.dashboard-w1 .details {
  text-align: right;
}

.dashboard-w1 .details .status,
.dashboard-w1 .details .amount,
.dashboard-w1 .details .currency-sign {
  color: #ffffff;
  font-size: 24px;
  font-weight: 500;
  line-height: 1;
}

.dashboard-w1 .details .description span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 300;
  display: inline-block;
  margin-top: 5px;
}

/* widget css end */
/* media css start */
.avatar img {
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  -o-object-position: center;
}

.avatar.avatar--xl img {
  width: 75px;
  height: 75px;
}

.avatar.avatar--lg img {
  width: 65px;
  height: 65px;
}

.avatar.avatar--md img {
  width: 55px;
  height: 55px;
}

.avatar.avatar--sm img {
  width: 45px;
  height: 45px;
}

.avatar.avatar--xs img {
  width: 35px;
  height: 35px;
}

/* media css edn */

/* sidebar css start */
.sidebar {
  width: 250px;
  background: #ffffff;
  border-right: 1px solid #66666675;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 999;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

@media (max-width: 991px) {
  .sidebar {
    left: -285px;
  }
}

.sidebar.open {
  left: 0;
}

.res-sidebar-open-btn {
  width: 35px;
  padding: 0;
  color: #fff;
  font-size: 20px;
  background-color: transparent;
  display: none;
}

@media (max-width: 991px) {
  .res-sidebar-open-btn {
    display: inline-block;
  }
}

.sidebar .res-sidebar-close-btn {
  position: absolute;
  top: 0;
  right: -35px;
  width: 35px;
  height: 35px;
  background-color: #4634ff;
  color: #ffffff;
  display: none;
}

@media (max-width: 991px) {
  .sidebar .res-sidebar-close-btn {
    display: grid;
    place-content: center;
  }
}

.sidebar .res-sidebar-close-btn i {
  font-size: 24px;
}

.sidebar .sidebar__logo-shape {
  display: none;
}

.sidebar .sidebar__logo-shape img {
  height: 35px;
}

.sidebar .slimScrollDiv .slimScrollBar {
  background-color: #4634ff !important;
  width: 5px !important;
  opacity: 1 !important;
}

.capsule--block .sidebar-menu-item.active a,
.capsule--block .sidebar-menu-item.sidebar-dropdown .side-menu--open {
  border-left: 3px solid #4634ff;
  border-radius: 0;
}

.capsule--block .sidebar-submenu .sidebar-menu-item.active a {
  border-left: none;
  background-color: transparent;
}

.sidebar.capsule--rounded .sidebar__menu {
  padding-left: 0;
}

.sidebar.capsule--rounded .sidebar__menu .sidebar-menu-item a {
  text-decoration: none;
}

.sidebar.capsule--rounded .sidebar__menu>.sidebar-menu-item>a {
  margin-right: 5px;
  border-radius: 0 999px 999px 0;
  -webkit-border-radius: 0 999px 999px 0;
  -moz-border-radius: 0 999px 999px 0;
  -ms-border-radius: 0 999px 999px 0;
  -o-border-radius: 0 999px 999px 0;
}

.sidebar.capsule--rounded .sidebar__menu>.sidebar-menu-item .sidebar-submenu .sidebar-menu-item {
  margin-right: 0;
}

.sidebar.capsule--rounded .sidebar__menu>.sidebar-menu-item .sidebar-submenu .sidebar-menu-item>a {
  margin-right: 15px;
  margin-top: 2px;
  border-radius: 0 999px 999px 0;
  -webkit-border-radius: 0 999px 999px 0;
  -moz-border-radius: 0 999px 999px 0;
  -ms-border-radius: 0 999px 999px 0;
  -o-border-radius: 0 999px 999px 0;
}

.sidebar.capsule--rounded2 .sidebar__menu>.sidebar-menu-item>a {
  border-radius: 999px;
  -webkit-border-radius: 999px;
  -moz-border-radius: 999px;
  -ms-border-radius: 999px;
  -o-border-radius: 999px;
}

.sidebar.capsule--rounded2 .sidebar__menu>.sidebar-menu-item .sidebar-submenu .sidebar-menu-item {
  margin-right: 0;
}

.sidebar.capsule--rounded2 .sidebar__menu>.sidebar-menu-item .sidebar-submenu .sidebar-menu-item>a {
  border-radius: 999px;
  -webkit-border-radius: 999px;
  -moz-border-radius: 999px;
  -ms-border-radius: 999px;
  -o-border-radius: 999px;
}

.sidebar__logo {
  padding: 20px 10px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  text-align: center;
  flex-shrink: 0;
}

.sidebar__logo .sidebar__main-logo {
  width: 90%;
}

.sidebar__logo .sidebar__main-logo img {
  max-height: 75px;
}

@media (max-width: 991px) {
  .sidebar__logo .sidebar__main-logo {
    width: 100%;
  }
}

.navbar__expand {
  margin-left: auto;
  background-color: transparent;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  position: relative;
  margin-top: 15px;
}

@media (max-width: 991px) {
  .navbar__expand {
    display: none;
  }
}

.navbar__expand.active::before {
  opacity: 0;
}

.navbar__expand::before {
  position: absolute;
  content: '';
  top: 4px;
  left: 4px;
  width: 7px;
  height: 7px;
  background-color: #5b6e88;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  opacity: 1;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

.navbar__expand::after {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1.5px solid #ffffff;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.sidebar-submenu {
  display: none;
}

.sidebar__menu {
  padding: 0 0px;
}

.sidebar__menu .sidebar-menu-item {
  margin-top: 5px;
}

.sidebar__menu .sidebar-menu-item>a {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  padding: 12px 25px;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.sidebar__menu .sidebar-menu-item>a:hover {
  background-color: #ebecf0;
  padding-left: 25px;
}

.sidebar__menu .sidebar-menu-item>a:hover .menu-icon {
  color: #4634ff;
  text-shadow: 1px 2px 5px #4634ff;
}

.sidebar__menu .sidebar-menu-item>a:hover .menu-title {
  color: #4634ff;
}

.sidebar__menu .sidebar-menu-item .side-menu--open,
.sidebar__menu .sidebar-menu-item.active>a {
  background-color: #4634ff !important;
}

.sidebar__menu .menu-icon {
  color: #5b6e88;
  font-size: 1.125rem;
  margin-right: 15px;
  transition: all 0.5s;
  text-shadow: 1px 2px 5px rgba(0, 0, 0, 0.15);
}

.sidebar__menu .menu-title {
  font-size: 0.8125rem;
  color: #5b6e88;
  letter-spacing: 0.5px;
}

.sidebar__menu .menu-badge {
  padding: 1px 6px;
  font-size: 0.625rem;
  font-weight: 500;
  border-radius: 3px;
  box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.2);
}

.sidebar__menu .sidebar-dropdown>a {
  padding-right: 40px;
  position: relative;
}

.sidebar__menu .sidebar-dropdown>a::before {
  position: absolute;
  top: 15px;
  right: 20px;
  font-family: 'Font Awesome 5 Free';
  font-weight: 700;
  content: '\f107';
  font-size: 13px;
  color: #5b6e88;
  transition: all 0.3s;
}

.sidebar__menu .sidebar-dropdown>a.side-menu--open::before {
  transform: rotate(180deg);
  top: 13px;
}

.sidebar__menu .sidebar-submenu {
  position: relative;
  background-color: #ffffff0f;
}

.sidebar__menu .sidebar-submenu__open {
  display: block;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item {
  margin-top: 0;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item.active>a {
  background-color: #4634ff59 !important;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item.active a .menu-icon {
  color: #4634ff;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item.active a .menu-title {
  color: #4634ff;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item a {
  padding: 10px 20px 10px 35px;
  transition: all 0.3s;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item a:hover .menu-icon {
  color: #4634ff;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item a:hover .menu-title {
  color: #4634ff;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item a .menu-icon {
  font-size: 0.75rem;
}

.sidebar__menu .sidebar-submenu .sidebar-menu-item a .menu-title {
  font-size: 0.75rem;
}

.sidebar__menu-header {
  font-size: 0.6875rem;
  text-transform: uppercase;
  font-weight: 700;
  color: #b5b5b5;
  margin: 25px 0 8px 25px;
}

.sidebar[class*='bg--'] .sidebar__logo {
  border-color: rgba(255, 255, 255, 0.15);
}

.sidebar[class*='bg--'] .sidebar__menu .sidebar__menu-header {
  color: #b5b5b5;
}

.sidebar[class*='bg--'] .sidebar__menu .sidebar-menu-item>a:hover {
  background-color: #4634ff59;
}

.sidebar[class*='bg--'] .sidebar__menu .sidebar-menu-item .menu-icon,
.sidebar[class*='bg--'] .sidebar__menu .sidebar-menu-item .menu-title {
  color: #e0e0e0;
}

.sidebar[class*='bg--'] .sidebar__menu .sidebar-menu-item .side-menu--open {
  background-color: #4634ff !important;
}

.sidebar[class*='bg--'] .sidebar__menu .sidebar-dropdown>a::before {
  color: #e0e0e0;
}

.sidebar[class*='bg--'] .sidebar__menu .sidebar-submenu::before {
  border-left-color: #ffffff33;
}

.sidebar[class*='bg--'] .sidebar__menu .sidebar-submenu .sidebar-menu-item.active a .menu-icon,
.sidebar[class*='bg--'] .sidebar__menu .sidebar-submenu .sidebar-menu-item.active a .menu-title,
.sidebar[class*='bg--'] .sidebar__menu .sidebar-submenu .sidebar-menu-item:hover a .menu-icon,
.sidebar[class*='bg--'] .sidebar__menu .sidebar-submenu .sidebar-menu-item:hover a .menu-title {
  color: #ffffff;
}

.sidebar[class*='bg--']:not([class*='bg--gradi']).bg_img {
  background-image: none !important;
}

.sidebar[class*='bg--gradi'] .sidebar__menu .sidebar__menu-header {
  color: #dadada;
}

.sidebar[class*='bg--white'] {
  background-image: none !important;
}

.sidebar[class*='bg--white'] .sidebar__logo {
  border-color: #ececec;
}

.sidebar[class*='bg--white'] .sidebar__menu .sidebar__menu-header {
  color: #b5b5b5;
}

.sidebar[class*='bg--white'] .sidebar__menu .sidebar-menu-item.open {
  background-color: #f5f5f5;
}

.sidebar[class*='bg--white'] .sidebar__menu .sidebar-menu-item>a:hover {
  background-color: #ebecf0;
}

.sidebar[class*='bg--white'] .sidebar__menu .sidebar-menu-item>a:hover .menu-icon,
.sidebar[class*='bg--white'] .sidebar__menu .sidebar-menu-item>a:hover .menu-title {
  color: #4634ff;
}

.sidebar[class*='bg--white'] .sidebar__menu .sidebar-menu-item .menu-icon,
.sidebar[class*='bg--white'] .sidebar__menu .sidebar-menu-item .menu-title {
  color: #5b6e88;
}

.sidebar[class*='bg--white'] .sidebar__menu .sidebar-dropdown>a::before {
  color: #5b6e88;
}

.sidebar.pill--bg-1 .sidebar-menu-item .side-menu--open,
.sidebar.pill--bg-1 .sidebar-menu-item.active>a {
  background-color: #4634ff;
}

.sidebar.pill--bg-2 .sidebar-menu-item .side-menu--open,
.sidebar.pill--bg-2 .sidebar-menu-item.active>a {
  background-color: #1e9ff2;
}

.sidebar.pill--bg-3 .sidebar-menu-item .side-menu--open,
.sidebar.pill--bg-3 .sidebar-menu-item.active>a {
  background-color: #eb2222;
}

.sidebar.pill--gradi-1 .sidebar-menu-item .side-menu--open,
.sidebar.pill--gradi-1 .sidebar-menu-item.active>a {
  background-image: -moz-linear-gradient(14deg, #4656bb 18%, #9f05e7 82%) !important;
  background-image: -webkit-linear-gradient(14deg, #4656bb 18%, #9f05e7 82%) !important;
  background-image: -ms-linear-gradient(14deg, #4656bb 18%, #9f05e7 82%) !important;
}

.sidebar.pill--gradi-2 .sidebar-menu-item .side-menu--open,
.sidebar.pill--gradi-2 .sidebar-menu-item.active>a {
  background-image: -moz-linear-gradient(19deg, #ec398b 0%, #9948a3 41%, #4656bb 99%) !important;
  background-image: -webkit-linear-gradient(19deg, #ec398b 0%, #9948a3 41%, #4656bb 99%) !important;
  background-image: -ms-linear-gradient(19deg, #ec398b 0%, #9948a3 41%, #4656bb 99%) !important;
}

.sidebar.pill--gradi-3 .sidebar-menu-item .side-menu--open,
.sidebar.pill--gradi-3 .sidebar-menu-item.active>a {
  background-image: -moz-linear-gradient(-177deg, #f24341 0%, #cd2c5b 53%, #a71574 100%) !important;
  background-image: -webkit-linear-gradient(-177deg, #f24341 0%, #cd2c5b 53%, #a71574 100%) !important;
  background-image: -ms-linear-gradient(-177deg, #f24341 0%, #cd2c5b 53%, #a71574 100%) !important;
}

.sidebar .version-info {
  width: 249px;
  padding-block: 10px;
  background-color: #071251;
  transition: 0.27s;
  flex-shrink: 0;
}

.sidebar__menu-wrapper {
  flex: 1;
  overflow-y: auto;
}

@media (max-width: 991px) {
  .sidebar:not(.open) .version-info {
    left: -100%;
  }
}

/* sidebar css end */
/* navbar-wrapper css start */
.navbar-wrapper {
  position: relative;
  background: #ffffff;
  padding: 15px 30px;
  margin-left: 250px;
  border-bottom: 1px solid #dee4ec;
  transition: all 0.5s cubic-bezier(0.4, -0.25, 0.25, 1.1);
}

@media (max-width: 991px) {
  .navbar-wrapper {
    margin-left: 0;
  }
}

@media (max-width: 575px) {
  .navbar-wrapper {
    padding: 10px 10px;
  }
}

*[class*='bg--']:not(.bg--white) .fullscreen-btn,
*[class*='bg--']:not(.bg--white) .navbar__right button i,
*[class*='bg--']:not(.bg--white) .navbar-user__name {
  color: #ffffff;
}

.navbar__left {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.navbar-search {
  position: relative;
  width: 300px;
}

.navbar-search .navbar-search-field {
  background-color: rgba(255, 255, 255, 0.05);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 5px;
  padding-left: 40px;
  width: 100%;
}

.navbar-search .navbar-search-field::placeholder,
.navbar-search .navbar-search-field::-moz-placeholder,
.navbar-search .navbar-search-field::-webkit-input-placeholder {
  color: #f1f1f1;
}

.navbar-search .navbar-search-field::-webkit-search-decoration,
.navbar-search .navbar-search-field::-webkit-search-cancel-button,
.navbar-search .navbar-search-field::-webkit-search-results-button,
.navbar-search .navbar-search-field::-webkit-search-results-decoration {
  display: none;
}

.navbar-search .navbar-search-field:focus {
  border-color: #4634ff;
  box-shadow: 0 0 5px #4634ff80;
}

.navbar-search i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
}

.navbar-search .autocomplete-items {
  position: absolute;
  top: 100%;
  left: 0;
  width: 300px;
  background-color: #fff;
  -webkit-border-radius: 0 0 5px 5px;
  -moz-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
  z-index: 999;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e5e5;
  border-top: none;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ddd #fff;
}

.navbar-search .autocomplete-items::-webkit-scrollbar {
  width: 10px;
}

.navbar-search .autocomplete-items::-webkit-scrollbar-track {
  background: #fff;
}

.navbar-search .autocomplete-items::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 20px;
  border: 2px solid #fff;
}

.navbar-search .autocomplete-items>div {
  border-bottom: 1px dashed #e5e5e5;
}

.navbar-search .autocomplete-items>div:last-child {
  border-bottom: none;
}

.navbar-search .autocomplete-items>div a {
  color: #777;
  padding: 10px 15px;
  display: block;
  transition: all 0.3s;
}

.navbar-search .autocomplete-items>div:hover a,
.navbar-search .autocomplete-items>div.autocomplete-active a {
  background-color: #f3f3f3;
}

.navbar__right {
  margin-left: auto;
}

@media (max-width: 767px) {

  .profile-dropdown {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }

  .navbar__left,
  .navbar__right {
    width: 100%;
  }

  .navbar__right {
    margin-top: 15px;
  }

  .navbar-search {
    flex: 1;
  }
}

.navbar__right button {
  background-color: transparent;
  position: relative;
}

.navbar__right button i {
  color: #5b6e88;
  text-shadow: 1px 2px 5px rgba(0, 0, 0, 0.15);
  font-size: 1.5rem;
}

@media (max-width: 420px) {
  .navbar__right .dropdown-menu {
    position: fixed;
    top: 60px;
    left: 15px;
    right: 15px;
    width: 91% !important;
    /* min-width: 91% !important; */
  }
}

.navbar-user {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
}

.navbar-user__thumb {
  width: 35px;
}

.navbar-user__thumb img {
  width: 35px;
  height: 35px;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.navbar-user__info {
  text-align: left;
  padding-left: 10px;
}

.navbar-user__name {
  font-size: 0.75rem;
  font-weight: 700;
  color: #34495e;
}

.navbar-user__desig {
  font-size: 0.75rem;
}

.navbar-user .icon {
  padding-left: 10px;
}

.navbar-user .icon i {
  font-size: 0.875rem;
}

.navbar__action-list {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
}

.navbar__action-list li {
  margin-right: 10px;
}

@media (max-width: 575px) {
  .navbar__action-list li {
    margin-right: 5px;
  }
}

.navbar__action-list li:last-child {
  margin-right: 0;
}

.navbar__action-list .nice-select {
  padding: 0 12px 0 0 !important;
  border: none;
  height: 30px !important;
  min-height: 30px !important;
  line-height: 30px !important;
}

.navbar__action-list .nice-select::after {
  right: 0;
  margin-top: -3px;
}

.dropdown-menu.dropdown-menu--md {
  min-width: 18rem;
}

.dropdown-menu.dropdown-menu--sm {
  min-width: 12rem;
}

.dropdown-menu__header {
  padding: 15px 15px;
  border-bottom: 1px solid #e5e5e5;
}

.dropdown-menu__header .caption {
  font-size: 0.75rem;
  font-weight: 700;
}

.dropdown-menu__header p {
  font-size: 0.75rem;
}

.dropdown-menu__item {
  display: block;
  border-bottom: 1px solid #e5e5e5;
}

.dropdown-menu__item:hover {
  background-color: #f7f7f7;
}

.dropdown-menu__item .dropdown-menu__icon {
  font-size: 1.25rem;
  color: #34495e;
  text-shadow: 1px 2px 5px rgba(0, 0, 0, 0.15);
}

.dropdown-menu__item .dropdown-menu__caption {
  color: #34495e;
  font-size: 0.875rem;
  font-weight: 500;
}

.dropdown-menu__item .dropdown-menu__icon~.dropdown-menu__caption {
  padding-left: 8px;
}

.dropdown-menu .slimScrollDiv .slimScrollBar {
  background-color: #000000 !important;
  width: 3px !important;
  opacity: 0.15 !important;
}

.dropdown-menu__footer {
  border-top: 1px solid #e5e5e5;
}

.dropdown-menu__footer .view-all-message {
  font-size: 0.75rem;
  display: block;
  padding: 15px 15px;
  text-align: center;
  color: #34495e;
  font-weight: 600;
}

.dropdown-menu__footer .view-all-message:hover {
  color: #4634ff;
}

.message-notifi {
  padding: 15px 15px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.message-notifi__left {
  width: 45px;
}

.message-notifi__left img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  overflow: hidden;
  object-fit: cover;
  -o-object-fit: cover;
  object-position: center;
  -o-object-position: center;
}

.message-notifi__right {
  width: calc(100% - 45px);
  padding-left: 10px;
}

.message-notifi__right .name {
  font-weight: 500;
  font-size: 0.875rem;
  color: #34495e;
}

.message-notifi__right p {
  color: #5b6e88;
  font-size: 0.8125rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-notifi__right .time {
  font-size: 0.6875rem;
  font-weight: 600;
}

.navbar-notifi {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 15px 15px;
}

.navbar-notifi__left {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.navbar-notifi__left img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.navbar-notifi__left i {
  font-size: 1.35rem;
}

.navbar-notifi__right .notifi__title {
  font-weight: 600;
  font-size: 0.875rem;
}

.navbar-notifi__right .time {
  font-size: 0.75rem;
  margin-top: 5px;
}

.pulse--primary {
  display: block;
  position: absolute;
  top: 3px;
  right: 7px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #4634ff;
  cursor: pointer;
  box-shadow: 0 0 0 rgba(115, 103, 240, 0.9);
  animation: pulse-primary 2s infinite;
  animation-duration: 0.9s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-out;
}

@-webkit-keyframes pulse-primary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(115, 103, 240, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0);
  }
}

@-moz-keyframes pulse-primary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(115, 103, 240, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0);
  }
}

@-ms-keyframes pulse-primary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(115, 103, 240, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0);
  }
}

@keyframes pulse-primary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(115, 103, 240, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(115, 103, 240, 0);
  }
}

.pulse--secondary {
  display: block;
  position: absolute;
  top: 3px;
  right: 7px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #868e96;
  cursor: pointer;
  box-shadow: 0 0 0 rgba(134, 142, 150, 0.9);
  animation: pulse-secondary 2s infinite;
  animation-duration: 0.9s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-out;
}

@-webkit-keyframes pulse-secondary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(134, 142, 150, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0);
  }
}

@-moz-keyframes pulse-secondary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(134, 142, 150, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0);
  }
}

@-ms-keyframes pulse-secondary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(134, 142, 150, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0);
  }
}

@keyframes pulse-secondary {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(134, 142, 150, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(134, 142, 150, 0);
  }
}

.pulse--info {
  display: block;
  position: absolute;
  top: 3px;
  right: 7px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #1e9ff2;
  cursor: pointer;
  box-shadow: 0 0 0 rgba(30, 159, 242, 0.9);
  animation: pulse-info 2s infinite;
  animation-duration: 0.9s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-out;
}

@-webkit-keyframes pulse-info {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(30, 159, 242, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0);
  }
}

@-moz-keyframes pulse-info {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(30, 159, 242, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0);
  }
}

@-ms-keyframes pulse-info {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(30, 159, 242, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0);
  }
}

@keyframes pulse-info {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(30, 159, 242, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(30, 159, 242, 0);
  }
}

.pulse--warning {
  display: block;
  position: absolute;
  top: 3px;
  right: 7px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #ff9f43;
  cursor: pointer;
  box-shadow: 0 0 0 rgba(255, 159, 67, 0.9);
  animation: pulse-warning 2s infinite;
  animation-duration: 0.9s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-out;
}

@-webkit-keyframes pulse-warning {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(255, 159, 67, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0);
  }
}

@-moz-keyframes pulse-warning {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(255, 159, 67, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0);
  }
}

@-ms-keyframes pulse-warning {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(255, 159, 67, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0);
  }
}

@keyframes pulse-warning {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(255, 159, 67, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 159, 67, 0);
  }
}

.pulse--danger {
  display: block;
  position: absolute;
  top: 3px;
  right: 7px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #eb2222;
  cursor: pointer;
  box-shadow: 0 0 0 rgba(238, 51, 94, 0.9);
  animation: pulse-danger 2s infinite;
  animation-duration: 0.9s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-out;
}

@-webkit-keyframes pulse-danger {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(220, 53, 69, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

@-moz-keyframes pulse-danger {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(220, 53, 69, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

@-ms-keyframes pulse-danger {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(220, 53, 69, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

@keyframes pulse-danger {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.9);
  }

  70% {
    -webkit-box-shadow: 0 0 0 6px rgba(220, 53, 69, 0);
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

/* navbar-wrapper css end */
/* body-wrapper css start */
.body-wrapper {
  margin-left: 250px;
  padding: 30px;
  transition: all 0.5s cubic-bezier(0.4, -0.25, 0.25, 1.1);
}

@media (max-width: 575px) {
  .body-wrapper {
    padding: 30px 10px;
  }
}

.body-wrapper.active {
  margin-left: 80px;
}

@media (max-width: 991px) {
  .body-wrapper {
    margin-left: 0;
  }
}

.page-title {
  font-size: 1.125rem;
  display: inline-block;
}

.page-breadcrumb {
  padding: 0;
  margin: 0;
  background-color: transparent;
}

.page-breadcrumb li {
  font-size: 0.875rem;
}

.page-breadcrumb li a {
  color: #4634ff;
}

#world-map-markers {
  height: 400px;
}

.pricing-table .price {
  font-size: 52px;
}

.package-features-list {
  display: inline-block;
  text-align: left;
}

.package-features-list li+li {
  margin-top: 20px;
}

.package-features-list li {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
}

.package-features-list li i {
  font-size: 11px;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.single-answer {
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.single-answer.from__admin .single-answer__header {
  background-color: rgba(40, 199, 111, 0.15);
}

.single-answer__header {
  padding: 10px 30px;
  background-color: #f1f1f1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: space-between;
}

.single-answer__body {
  padding: 30px;
}

/* body-wrapper css end */
/* documentation css start */
.docs-wrapper .docs__title {
  font-weight: 600;
  margin-bottom: 10px;
}

.docs-wrapper .docs__subtitle {
  font-weight: 600;
}

.docs-wrapper .docs__info {
  color: #000000;
  padding: 5px 20px;
  background-color: rgba(115, 103, 240, 0.15);
  display: inline-block;
  box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.docs-wrapper .docs__info i {
  margin-right: 6px;
}

.docs-wrapper .docs__info b {
  color: #5b6e88;
  font-weight: 600;
}

.docs-wrapper p {
  font-size: 0.875rem;
}

.docs-wrapper pre {
  padding: 20px;
  background-color: #ffffff;
}

.docs-wrapper pre code {
  color: tomato;
}

/* documentation css end */
/* login css start */

.login-main {
  padding: 100px 0;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 100vh;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-color: #3d2bfb;
}

@media (max-width: 575px) {
  .login-main {
    padding: 50px 0;
  }
}

.login-main::before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #1e157d;
  opacity: 0.57;
  z-index: -1;
}

.login-area {
  position: relative;
}

.login-area::after {
  position: absolute;
  content: '';
  width: 247px;
  height: 247px;
  right: -80px;
  top: -100px;
  border: 40px solid rgba(52, 34, 229, 0.31);
  box-sizing: border-box;
  filter: blur(2px);
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.login-wrapper {
  background-color: #1e157d;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  overflow: hidden;
}

.login-wrapper__top {
  padding: 60px 30px 40px 30px;
  text-align: center;
  background-color: #3d2bfb;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  position: relative;
  z-index: 1;
}

@media (max-width: 575px) {
  .login-wrapper__top {
    padding: 40px 15px;
  }
}

.login-wrapper__top::after {
  position: absolute;
  content: '';
  bottom: -25px;
  left: 5px;
  right: 5px;
  z-index: -1;
  border-width: 25px 256px 0px 256px;
  border-style: solid;
  border-color: #3d2bfb transparent transparent transparent;
}

@media (max-width: 1399px) {
  .login-wrapper__top::after {
    border-width: 25px 218px 0px 218px;
  }
}


@media (max-width: 575px) {
  .login-wrapper__top::after {
    display: none;
  }
}

.login-wrapper__top .title {
  font-size: 30px;
  font-weight: 600;
}

@media (max-width: 575px) {
  .login-wrapper__top .title {
    font-size: 24px;
  }
}

@media (max-width: 360px) {
  .login-wrapper__top .title {
    font-size: 22px;
  }
}

.login-wrapper__top p {
  font-size: 17px;
}

@media (max-width: 575px) {
  .login-wrapper__top p {
    font-size: 14px;
  }
}

.login-wrapper__body {
  padding: 60px 40px 40px 40px;
}

@media (max-width: 575px) {
  .login-wrapper__body {
    padding: 30px 20px;
  }
}

.login-form label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  font-weight: 500;
}

.login-form label.form-check-label {
  font-size: 14px;
}

.login-form .form-control {
  height: 50px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 16px;
  padding: 20px;
}

.login-form .form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.31) !important;
}

.login-form .form-control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.31) !important;
}

.login-form .form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.31) !important;
}

.login-form .form-control:-moz-placeholder {
  color: rgba(255, 255, 255, 0.31) !important;
}

.login-form .form-control:focus {
  background-color: transparent;
  border-color: #3d2bfb;
}

.login-form .forget-text {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: underline;
}

.login-form .forget-text:hover {
  color: #fff;
}

.login-form .cmn-btn {
  margin-top: 15px;
}

.cmn-btn {
  background-color: #3d2bfb;
  color: #fff;
  height: 50px;
}

.cmn-btn:hover,
.btn:focus-visible,
.cmn-btn:active {
  background-color: #2916ed !important;
  color: #fff !important;
  border-color: transparent !important;
}

.login-form .form-check {
  position: relative;
  padding-left: 0;
}

.login-form .form-check .form-check-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100% !important;
  margin-left: 0;
  opacity: 0;
}

.login-form .form-check .form-check-label {
  position: relative;
  padding-left: 25px;
}

.login-form .form-check .form-check-label::before {
  position: absolute;
  content: '';
  font-family: 'Line Awesome Free';
  font-weight: 900;
  top: 3px;
  left: 0;
  width: 15px;
  height: 15px;
  border: 1px solid #fff;
  border-radius: 3px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  transition: all 0.3s;
}

.login-form .form-check .form-check-input:checked~.form-check-label::before {
  content: '\f00c';
  background-color: #3d2bfb;
  border-color: #3d2bfb;
}

/* login css end */
/* error css start */
.error-area {
  height: 100vh;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.error-area .error-wrapper {
  width: 750px;
  text-align: center;
}

/* error css end */

/* sourceMappingURL=app.css.map */

.badge {
  font-size: 0.75rem !important;
  font-weight: 300;
}

.badge-dot i {
  width: 7px;
  height: 7px;
  border-radius: 50%;
  display: inline-block;
}

.icon-btn {
  padding: 3px 8px;
  background-color: #4634ff;
  color: #ffffff;
  border-radius: 3px;
  font-size: 13px;
}

a.icon-btn {
  padding: 4.5px 7px;
}

.icon-btn:hover {
  color: #ffffff;
}

.badge--warning,
.badge--success,
.badge--primary,
.badge--danger,
.badge--info,
.badge--dark {
  border-radius: 999px;
  padding: 2px 15px;
  position: relative;
  border-radius: 999px;
  -webkit-border-radius: 999px;
  -moz-border-radius: 999px;
  -ms-border-radius: 999px;
  -o-border-radius: 999px;
}

.badge--warning {
  background-color: rgba(255, 159, 67, 0.1);
  border: 1px solid #ff9f43;
  color: #ff9f43;
}

.badge--success {
  background-color: rgba(40, 199, 111, 0.1);
  border: 1px solid #28c76f;
  color: #28c76f;
}

.badge--danger {
  background-color: rgba(234, 84, 85, 0.1);
  border: 1px solid #eb2222;
  color: #eb2222;
}

.badge--primary {
  background-color: rgba(115, 103, 240, 0.1);
  border: 1px solid #4634ff;
  color: #4634ff;
}

.badge--dark {
  background-color: rgba(0, 0, 0, 0.1);
  border: 1px solid #000000;
  color: #000000;
}

.badge--info {
  background-color: rgba(30, 159, 242, 0.1);
  border: 1px solid #1e9ff2;
  color: #1e9ff2;
}

.payment-method-item .payment-method-header {
  display: flex;
  flex-wrap: wrap;
}

.payment-method-item .payment-method-header .thumb {
  width: 180px;
  position: relative;
  margin-bottom: 30px;
}

.payment-method-item .payment-method-header .thumb .image-upload-wrapper {
  height: 180px;
}

.payment-method-item .payment-method-header .thumb .image--uploader small {
  font-size: 12px;
}

.payment-method-item.child--item .payment-method-header .thumb {
  width: 165px;
}

.payment-method-item .payment-method-header .content {
  width: 100%;
}

@media screen and (max-width: 1199px) {
  .payment-method-header .remove-btn {
    order: 1;
    text-align: right;
    flex-shrink: 0;
  }

  .payment-method-title {
    order: 2;
  }
}

.payment-method-item .payment-method-header .content .input-group select {
  width: auto;
  padding-left: 15px;
  padding-right: 15px;
  border-radius: 5px 0 0 5px !important;
}

.payment-method-item .payment-method-header .content p {
  font-size: 20px;
  margin-top: 15px;
}

.payment-method-item {
  padding: 50px 0;
  border-bottom: 2px solid #e5e5e5;
}

.payment-method-item:first-child {
  padding-top: 0;
}

.payment-method-item:last-child {
  padding-bottom: 0 !important;
  border-bottom: 0 !important;
}

.payment-method-title-input {
  max-width: 320px;
  width: 100%;
}

@media (max-width: 1199px) {
  .payment-method-header .content .title {
    margin-bottom: 20px;
  }

  .payment-method-header .content .input-group {
    justify-content: flex-start !important;
  }
}

@media (max-width: 767px) {
  .payment-method-item .payment-method-header .content {
    width: 100%;
    padding-left: 0;
  }

  .payment-method-item .payment-method-header .content p {
    font-size: 16px;
  }
}

@media (max-width: 575px) {
  .navbar-nav #userProfileDropdown+.dropdown-menu {
    right: 0 !important;
  }

  .payment-method-item .payment-method-header .content>.d-flex {
    flex-direction: column;
  }

  .payment-method-item .payment-method-header .content>.d-flex .input-group {
    order: -1;
    justify-content: flex-start !important;
    margin-bottom: 20px;
  }

  .payment-method-item .payment-method-header .content>.d-flex .remove-btn {
    order: -1;
    margin-bottom: 15px;
  }
}

@media (max-width: 340px) {
  .payment-method-item .payment-method-header .content .input-group select {
    padding-left: 6px;
    padding-right: 6px;
  }

  .payment-method-item .payment-method-header .thumb {
    width: 100%;
  }
}

.payment-method-item .payment-method-header .content .input-group select {
  border: 1px solid #ced4da;
}

.gateway-body {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin-top: 25px;
  --thumb-size: 200px;
}

.gateway-thumb {
  width: var(--thumb-size);
}

.gateway-thumb .image-upload-wrapper {
  height: 150px;
}

.gateway-thumb .image-upload-preview {
  background-size: contain;
}

.gateway-content {
  width: calc(100% - var(--thumb-size));
  padding-left: 30px;
}

@media screen and (max-width: 575px) {
  .gateway-content {
    width: 100%;
    padding-left: 0px;
    padding-top: 25px;
  }
}

.gateway-thumb .image-upload-wrapper+div {
  display: none;
}

.gateway-content .payment-method-body-title {
  font-size: 18px;
  font-weight: 600;
}
.payment-method-body .card{
  border-radius: 7px;
  -webkit-border-radius: 7px;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

#fileUploadsContainer {
  margin-top: 15px;
}

.file-upload-wrapper+.file-upload-wrapper {
  margin-top: 15px;
}

.file-upload-wrapper {
  position: relative;
  width: 100%;
  height: 40px;
}

.file-upload-wrapper:after {
  content: attr(data-text);
  font-size: 14px;
  position: absolute;
  top: 0;
  left: 0;
  background: #fff;
  padding: 0 15px;
  display: block;
  width: calc(100% - 40px);
  pointer-events: none;
  z-index: 20;
  height: 100%;
  line-height: 40px;
  color: #999;
  border-radius: 5px;
  font-weight: 300;
  border: 1px solid #e5e5e5;
}

.file-upload-wrapper:before {
  content: 'Upload';
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
  height: 100%;
  background: #4634ff;
  color: #fff;
  font-weight: 500;
  z-index: 25;
  font-size: 14px;
  line-height: 40px;
  padding: 0 15px;
  pointer-events: none;
  border-radius: 0 5px 5px 0;
}

.file-upload-wrapper input {
  opacity: 0;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  height: 40px;
  margin: 0;
  padding: 0;
  display: block;
  cursor: pointer;
  width: 100%;
}

.search-list {
  position: absolute;
  top: 100%;
  background-color: #fff;
  width: 100%;
  z-index: 99;
  max-height: 310px;
  overflow: auto;
  border-radius: 0 0 5px 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.search-list::-webkit-scrollbar {
  width: 2px;
}

.search-list::-webkit-scrollbar-track {
  box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
}

.search-list::-webkit-scrollbar-thumb {
  background-color: darkgrey;
  outline: 1px solid slategrey;
}

.search-list li {
  border-bottom: 1px solid #e5e5e5;
}

.search-list li .search-list-link {
  padding: 8px 8px 8px 30px;
  display: block;
}

.search-list li.active .search-list-link,
.search-list li .search-list-link:hover {
  background-color: #eee;
}

.search-list li a {
  color: #363636;
  font-size: 13px;
}

.plugin_bg {
  width: auto;
  height: 65px;
  border-radius: 75%;
}

.deposit-imgView {
  max-width: 100px;
  max-height: 100px;
  margin: 0 auto;
}

.width-375 {
  max-width: 375px;
}

.admin-bg-reply {
  background-color: #faf8f1;
}

.withdraw-detailImage {
  max-width: 100px;
  max-height: 100px;
  margin: 0 auto;
}

.font-20 {
  font-size: 20px;
}

/*Notification Css*/
.notify__item {
  display: block;
  text-decoration: none !important;
  align-items: center;
  padding: 10px 15px;
  padding-right: 55px;
  background: #fff;
  border: 1px solid #e5e5e5;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  transition: all ease 0.3s;
}

.notify__item:not(:last-child) {
  margin-bottom: 5px;
}

.notify__item .notify__thumb {
  width: 50px;
  height: 50px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  overflow: hidden;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.notify__item .notify__thumb i {
  color: #fff;
  font-size: 20px;
}

.notify__item .notify__thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.notify__item .notify__content {
  padding-left: 15px;
  color: #555555;
}

.notify__item .notify__content .title {
  font-size: 16px;
  margin: 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  overflow: hidden;
}

.notify__item .notify__content .info {
  font-size: 14px;
  line-height: 1.4;
  display: block;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  overflow: hidden;
}

.notify__item .notify__content .date {
  font-size: 12px;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.notify__item .notify__content .date i {
  color: #f74a05;
  font-size: 18px;
  margin-right: 5px;
}

.notify__item:hover {
  background: rgba(115, 103, 240, 0.1);
}

.unread--notification {
  background-color: #d6d6e633;
}

.menu-badge {
  color: #fff !important;
}

.icon-left-right {
  animation: ring 2s infinite ease;
}

@keyframes ring {
  0% {
    transform: rotate(35deg);
  }

  12.5% {
    transform: rotate(-30deg);
  }

  25% {
    transform: rotate(25deg);
  }

  37.5% {
    transform: rotate(-20deg);
  }

  50% {
    transform: rotate(15deg);
  }

  62.5% {
    transform: rotate(-10deg);
  }

  75% {
    transform: rotate(5deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

.modal .close {
  background: transparent;
}

label.required:after {
  content: '*';
  color: #dc3545 !important;
  margin-left: 2px;
}

.iconpicker-popover.fade {
  opacity: 1;
}

.sp-replacer {
  padding: 0;
  border: none;
  border-radius: 5px 0 0 5px;
}

.sp-preview {
  width: 100px;
  height: 45px;
  border: 0;
}

.sp-preview-inner {
  width: 110px;
}

.sp-dd {
  display: none;
}

.mail-wrapper {
  position: relative;
  overflow: hidden;
}

.mailsent {
  display: block;
  -webkit-animation: mailsent-movement 3.5s infinite ease-in-out;
  position: absolute;
  left: 0;
  right: 0;
  top: 30%;
}

.mailsent .envelope {
  font-size: 20px;
  margin-left: -25px;
  width: 50px;
  height: 20px;
  top: 50%;
  transform-origin: 50% 50%;
  -webkit-animation: mailsent-rotation 3.5s infinite ease-in-out;
  font-size: 22px;
}

.mailsent .envelope i {
  color: #4634ff;
}

.mailsent .envelope .icon {
  position: absolute;
  left: 0;
}

.mailsent .envelope .line {
  position: absolute;
  width: 10px;
  height: 2px;
  background: #4634ff;
  display: block;
}

.mailsent .envelope .line.line1 {
  width: 15px;
  top: 4px;
  left: -20px;
  -webkit-animation: line-size-large 3.5s infinite ease-in-out;
}

.mailsent .envelope .line.line2 {
  width: 10px;
  top: 9px;
  left: -20px;
  -webkit-animation: line-size-small 3.5s infinite ease-in-out;
}

.mailsent .envelope .line.line3 {
  width: 15px;
  top: 14px;
  left: -20px;
  -webkit-animation: line-size-large 3.5s infinite ease-in-out;
}

@-moz-keyframes mailsent-movement {
  0% {
    transform: translate(-50%);
    opacity: 0.1;
  }

  10% {
    transform: translate(-50%);
    opacity: 1;
  }

  50% {
    transform: translate(50%);
  }

  90% {
    transform: translate(50%);
    opacity: 1;
  }

  100% {
    transform: translate(150%);
    opacity: 0.1;
  }
}

@-webkit-keyframes mailsent-movement {
  0% {
    transform: translate(-50%);
  }

  10% {
    transform: translate(-50%);
    opacity: 1;
  }

  50% {
    transform: translate(50%);
  }

  90% {
    transform: translate(50%);
    opacity: 1;
  }

  100% {
    transform: translate(150%);
  }
}

@-o-keyframes mailsent-movement {
  0% {
    transform: translate(-50%);
  }

  50% {
    transform: translate(50%);
  }

  100% {
    transform: translate(150%);
  }
}

@keyframes mailsent-movement {
  0% {
    transform: translate(-50%);
  }

  50% {
    transform: translate(50%);
  }

  100% {
    transform: translate(150%);
  }
}

@-moz-keyframes mailsent-rotation {
  0% {
    transform: skewX(-35deg);
  }

  50% {
    transform: skewX(0deg);
  }

  100% {
    transform: skewX(-35deg);
  }
}

@-webkit-keyframes mailsent-rotation {
  0% {
    transform: skewX(-30deg);
  }

  50% {
    transform: skewX(0deg);
  }

  100% {
    transform: skewX(-30deg);
  }
}

@-o-keyframes mailsent-rotation {
  0% {
    transform: skewX(-30deg);
  }

  50% {
    transform: skewX(0deg);
  }

  100% {
    transform: skewX(-30deg);
  }
}

@keyframes mailsent-rotation {
  0% {
    transform: skewX(-30deg);
  }

  50% {
    transform: skewX(0deg);
  }

  100% {
    transform: skewX(-30deg);
  }
}

@-moz-keyframes line-size-large {
  0% {
    width: 15px;
    left: -20px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 15px;
    left: -20px;
  }
}

@-webkit-keyframes line-size-large {
  0% {
    width: 15px;
    left: -20px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 15px;
    left: -20px;
  }
}

@-o-keyframes line-size-large {
  0% {
    width: 15px;
    left: -20px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 15px;
    left: -20px;
  }
}

@keyframes line-size-large {
  0% {
    width: 15px;
    left: -20px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 15px;
    left: -20px;
  }
}

@-moz-keyframes line-size-small {
  0% {
    width: 10px;
    left: -15px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 10px;
    left: -15px;
  }
}

@-webkit-keyframes line-size-small {
  0% {
    width: 10px;
    left: -15px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 10px;
    left: -15px;
  }
}

@-o-keyframes line-size-small {
  0% {
    width: 10px;
    left: -15px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 10px;
    left: -15px;
  }
}

@keyframes line-size-small {
  0% {
    width: 10px;
    left: -15px;
  }

  50% {
    width: 0;
    left: -5px;
  }

  55% {
    width: 0;
    left: -5px;
  }

  100% {
    width: 10px;
    left: -15px;
  }
}

.progress {
  height: 25px;
  margin-bottom: 5px;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #4634ff;
}

.mail-icon {
  font-size: 60px;
  color: #4634ff;
  position: relative;
  z-index: 2;
  background: #fff;
}

.mail-wrapper {
  max-width: 350px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
}

.sidebar__inner {
  position: relative;
  z-index: 11111;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100vh;
}

.sidebar__menu-wrapper::-webkit-scrollbar {
  width: 5px;
}

.sidebar__menu-wrapper::-webkit-scrollbar-track {
  background: #071251;
}

.sidebar__menu-wrapper::-webkit-scrollbar-thumb {
  background: #4634ff;
}

.sidebar__menu-wrapper::-webkit-scrollbar-thumb:hover {
  background: #4634ff !important;
}

.sidebar__menu-wrapper::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.show-filter {
  display: none;
}

@media (max-width: 767px) {
  .responsive-filter-card {
    display: none;
    transition: none;
  }

  .show-filter {
    display: block;
  }
}

.short-codes {
  position: relative;
}

.short-codes:before {
  content: 'Copy';
  position: absolute;
  height: 0%;
  width: 100%;
  background: #4634ff;
  overflow: hidden;
  color: #fff;
  cursor: pointer;
  text-align: center;
  border-radius: 3px;
}

.short-codes.copied:before {
  content: 'Copied';
}

.short-codes:hover:before {
  height: 100%;
}

.select2-container {
  width: 100% !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f0f5f8;
  border-color: #d7d7d7;
}

.select2-container .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 45px;
  line-height: 45px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display,
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove span {
  color: #282828;
  font-size: 13px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  border-right-color: #d7d7d7;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #5e50ee;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  background-color: #dfdfdf;
}

.select2-container--default .select2-results__option--selected {
  background-color: #5e50ee;
}

.select2-container .select2-selection--multiple {
  min-height: 45px;
  padding-top: 5px;
}

.select2-dropdown {
  border: 1px solid #4634ff;
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  border: 1px solid #ced4da !important;
}

.select2-results__option.select2-results__option--selected {
  background-color: #f1f1f1;
}

@media (max-width: 365px) {
  .breadcrumb-plugins {
    justify-content: flex-start !important;
  }
}

.breadcrumb-plugins:has(.search-form) .btn {
  height: 45px;
  line-height: 2.3;
}

.breadcrumb-plugins:has(form) .btn {
  height: 45px;
  line-height: 2.5;
}

.search-list .bg--dark a {
  color: #fff !important;
}

.image--uploader {
  width: 240px;
  border-radius: 10px;
}

.image-upload-wrapper {
  height: 280px;
  position: relative;
}

.image-upload-preview {
  width: 100%;
  height: 100%;
  display: block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  border-radius: 10px;
  border: 1px solid #ced4da;
  box-shadow: none;
}

.image-upload-input-wrapper {
  position: absolute;
  display: inline-flex;
  bottom: -14px;
  right: -7px;
}

.image-upload-input-wrapper input {
  width: 0;
  opacity: 0;
}

.image-upload-input-wrapper label {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  text-align: center;
  border: 2px solid #fff;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
}

.image-upload-input-wrapper label::after {
  content: "";
}

.notification-via {
  text-align: center;
  border: 2px solid #cdcdcd;
  padding: 12px 0px;
  border-radius: 5px;
  background: #efefef;
  cursor: pointer;
  transition: all 0.3s;
}

.notification-via:hover {
  background: #e3e3e3;
}

.notification-via .send-via-method i {
  font-size: 30px;
  color: #626568;
}

.notification-via .send-via-method h5 {
  color: #626568;
}

.notification-via.active {
  border-color: #4634ff;
  position: relative;
}

.notification-via .active-badge {
  display: none;
}

.notification-via.active .active-badge {
  right: -1px;
  top: -1px;
  position: absolute;
  color: #fff;
  background: #4634ff;
  text-align: right;
  width: 50px;
  height: 40px;
  padding-right: 4px;
  clip-path: polygon(100% 0, 0 1%, 100% 100%);
  display: block;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  background-color: #f3f3f9;
  border-color: #dee2e6 #dee2e6 #f3f3f9;
}

.nav-tabs-primary {
  border: none;
}

.nav-tabs-primary .nav-item a {
  border: none;
}

.nav-tabs-primary .nav-item a.active {
  border-bottom: 2px solid #4634ff;
}

.breadcrumb-nav li.active a {
  color: #4634ff !important;
}

.breadcrumb-nav-open {
  font-size: 24px;
  line-height: 1;
  display: none;
  padding: 0;
  background-color: transparent;
  color: #fff;
  margin-left: 15px;
}

.breadcrumb-nav {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #5b6e8824;
  margin: -30px -30px 35px -30px;
  background-color: #fff;
  border-bottom: 1px solid #5352ed75;
}

.breadcrumb-nav li {
  margin-top: 3px;
}

.breadcrumb-nav li a {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  font-weight: 500;
  position: relative;
}

@media (max-width: 1399px) {
  .breadcrumb-nav li a {
    padding: 10px;
    font-size: 12px;
  }
}

.breadcrumb-nav li a::after {
  position: absolute;
  content: '';
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #eff1f9;
  opacity: 0;
  z-index: 1;
}

.breadcrumb-nav li.active a::after {
  opacity: 1;
}

.breadcrumb-nav li.active a {
  background-color: #f3f3f9;
  border-top: 1px solid #5352ed75;
  border-left: 1px solid #5352ed75;
  border-right: 1px solid #5352ed75;
  border-radius: 5px 5px 0 0;
}

.breadcrumb-nav li.active a:hover {
  border-top: 1px solid #5352ed75;
  border-left: 1px solid #5352ed75;
  border-right: 1px solid #5352ed75;
}

.breadcrumb-nav a i {
  color: #5b6e88;
  font-size: 18px;
  margin-right: 8px;
  margin-top: -3px;
}

.breadcrumb-nav a .menu-title {
  color: #5b6e88;
  line-height: 1;
}

.breadcrumb-nav a .menu-badge {
  font-size: 10px;
  padding: 0 5px;
  border-radius: 3px;
  margin-left: 5px;
}

.menu-badge-level-one {
  width: 18px;
  height: 18px;
  text-align: center;
}

.breadcrumb-nav li:hover a i,
.breadcrumb-nav li:hover a .menu-title,
.breadcrumb-nav li.active a i,
.breadcrumb-nav li.active a .menu-title {
  color: #5352ed;
}

@media (max-width: 1199px) {
  .breadcrumb-nav-open {
    display: inline-block;
  }

  .breadcrumb-nav {
    margin: 0;
    position: fixed;
    top: 30px;
    right: -390px;
    width: 300px;
    background-color: #fff;
    display: block;
    min-height: 100vh;
    z-index: 99;
    border: none;
    box-shadow: -5px 0 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
  }

  .breadcrumb-nav.active {
    right: 0;
  }

  .breadcrumb-nav li a {
    font-size: 13px;
    padding: 15px 18px;
  }

  .breadcrumb-nav li.active a {
    border: none;
  }

  .breadcrumb-nav li {
    margin-top: 0;
  }
}

@media (max-width: 380px) {
  .breadcrumb-nav {
    width: 250px;
  }

  .breadcrumb-nav li a {
    font-size: 12px;
    padding: 12px 12px;
  }
}

.breadcrumb-nav-close {
  position: absolute;
  top: 0;
  left: -35px;
  width: 35px;
  height: 35px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #5352ed;
  color: #fff;
  font-size: 24px;
}

.breadcrumb-nav li:not(.active) a:hover {
  border-color: transparent !important;
}

.breadcrumb-nav.nav-tabs .nav-link:focus {
  border-color: transparent !important;
}

.breadcrumb-nav.nav-tabs li.active .nav-link:focus {
  border-color: #5352ed75 !important;
}

.empty-notification img {
  width: 70px;
}

.form-change-alert {
  color: #000;
  padding: 5px;
  margin-bottom: 10px;
  border-radius: 3px;
  text-align: center;
}

.withdraw-method-image .image-upload-wrapper {
  height: 240px;
}

/* ////////////////// select 2 //////////////// */
.select2-dropdown {
  border: transparent;
  margin-top: 8px !important;
  border-radius: 5px !important;
  box-shadow: 0 3px 9px rgba(50, 50, 9, 0.05), 6px 4px 19px rgb(115 103 240 / 20%);
}

.select2-search--dropdown {
  padding: 10px 10px !important;
  border-color: #ced4da !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #ced4da !important;
  padding: 10px 20px;
}

.select2-results__option.select2-results__option--selected,
.select2-results__option--selectable {
  padding: 12px 14px !important;
  border-bottom: 1px solid #eee;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar {
  width: 8px;
  border-radius: 5px;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb {
  background: #ddd;
}

.select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb:hover {
  background: #ddd;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  display: none;
}

.select2-container--default .select2-selection--single .select2-selection__arrow:after {
  position: absolute;
  right: 10px;
  top: 0;
  content: '\f107';
  font-family: 'Line Awesome Free';
  font-weight: 900;
  transition: 0.3s;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow:after {
  transform: rotate(-180deg);
}

.select2-results__option:last-child {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.select2-results__option:first-child {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.select2-results__option.select2-results__option--selected {
  background-color: #f1f1f1;
  color: #000;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #f1f1f1 !important;
  color: #000;
}

.select2-container--open .select2-selection.select2-selection--single,
.select2-container--open .select2-selection.select2-selection--multiple {
  border-color: #4634ff !important;
  border-radius: 5px !important;
  box-shadow: 0 3px 9px rgba(50, 50, 9, 0.05), 3px 4px 8px rgba(115, 103, 240, 0.1);
}

.notify-item-wrapper {
  position: relative;
}

.notify-item-wrapper .notify-delete-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.btn-group a.btn {
  display: inline-flex;
  align-items: center;
  padding-inline: 15px;
}

.btn-group span.btn {
  pointer-events: none;
}

@media (max-width: 575px) {
  .body-wrapper {
    padding: 30px 0px;
  }
}

.btn--light {
  background-color: #F7F7F7 !important;
  color: #8F8F8F;
}

.btn--light:active {
  border-color: #F1F1F1 !important;
}

.btn--light:hover {
  background-color: #F1F1F1 !important;
}

.btn.btn--light:hover:hover {
  color: #8F8F8F !important;
}

.btn.btn-outline-light:hover:hover {
  color: #000 !important;
}

@media (max-width:1199px) {
  .breadcrumb-nav {
    margin: -30px -42px 35px -42px;
  }
}


@media screen and (max-width:575px) {
  .pagination {
    width: 100%;
    justify-content: center;
  }
}


.iziToast {
  font-family: 'Poppins', sans-serif !important;
}


.navbar-search .empty-search {
  padding: 20px;
}

.navbar-search .empty-search img {
  width: 70px;
  margin-bottom: 20px;
}

.empty-notification-list img {
  width: 120px;
  margin-bottom: 15px;
}

.notification-bell {
  position: relative;
}

.notification-count {
  position: absolute;
  top: 0;
  right: 0;
  height: 17px;
  width: 17px;
  background: #eb2222;
  font-size: 10px;
  color: #fff;
  border-radius: 50px;
  line-height: 17px;
}

.dropdown-menu {
  overflow: hidden;
}

.dropdown-menu__body {
  height: 270px;
  overflow-y: auto;
}

.dropdown-menu__body::-webkit-scrollbar {
  width: 4px;
}

.dropdown-menu__body::-webkit-scrollbar-thumb {
  background-color: rgb(7 18 81 / 40%);
}


/* configure css */

.flex-align {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.configure-card-wrapper{
  position: fixed;
  bottom: 20px;
  right: 20px !important;
  z-index: 999;
  display: flex;
  align-items: flex-end;
  gap: 12px;
  width: max-content;
  overflow: hidden;
}

.configure-card-handle {
  touch-action: none;
  user-select: none;
  cursor: grab;
}

@media (max-width: 575px) {
  .configure-card-wrapper{
    flex-direction: column;
    gap: 6px;
    max-width: 480px;
    width: calc(100% - 32px);
  }
}

.configure-card-btn{
  height: 64px;
  width: 64px;
  border-radius: 50%;
  background: linear-gradient(45deg, #044aff, #008eff);
  display: grid;
  place-content: center;
  cursor: pointer;
  color: #fff;
  flex-shrink: 0;
}

.configure-card-btn svg{
  animation: rotateAnimation 3s linear infinite;
}

@keyframes rotateAnimation {
  0%{
    rotate: 0deg;
  }
  100%{
    rotate: 360deg;
  }
}

.configure-card {
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  box-shadow: 1px 20px 20px 13px rgb(0 0 0 / 10%);
  background: #ffffff;
  transform: scale(0);
  transition: all linear 0.15s;
  transform-origin: bottom right;
}

.configure-card.show{
  animation: showCard linear 0.3s forwards; 
}

.configure-card.hide{
  animation: hideCard linear 0.3s backwards; 
}

@keyframes showCard {
  0%{
    transform: scale(0);
  }
  33%{
    transform: scale(1.05);
  }
  66%{
    transform: scale(0.95);
  }
  100%{
    transform: scale(1);
  }
}

@keyframes hideCard {
  0%{
    transform: scale(1);
  }
  33%{
    transform: scale(0.95);
  }
  66%{
    transform: scale(1.05);
  }
  100%{
    transform: scale(0);
  }
}


.configure-card:has(.show-btn.active) {
  border: 1px solid rgb(143 143 143 / 30%);
}

.configure-card-header {
  padding: 16px;
  border-radius: 12px;
  position: relative;
  z-index: 1;
  background-image: linear-gradient(45deg, #4634ff, #071251);
  background-image: linear-gradient(45deg, #044aff, #008eff);
}

.skip-configure {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.8rem;
  color: rgb(0 0 0 / 75%);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #fff;
  padding: 3px 7px;
  border-radius: 5px;
  box-shadow: 0px 5px 30px rgb(0 0 0 / 10%);
}

.skip-configure span {
  color: inherit;
}

.configure-card-title {
  color: #ffffff;
}

.configure-card-header .progress {
  background-color: rgb(255 255 255 / 20%);
  height: 8px;
}

.configure-card-header .progress-bar {
  height: 8px;
  background-color: #16def8;
}

.configure-status {
  display: flex;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 2px solid #16def8;
}

.configure-card-link {
  color: #fff;
  padding: 4px 6px;
  background-color: rgb(255 255 255 / 10%);
  border-radius: 4px;
  font-size: .7rem;
  font-weight: 500;
  transition: all linear 0.3s;
  border: 1px solid rgb(255 255 255 / 15%);
}

.configure-card-link span{
  color: #fff;
  transition: all linear 0.3s;
}

.configure-card-link:hover {
  background-color: #ffffff;
  border-color: #ffffff;
  color: #000;
}
.configure-card-link:hover span {
  color: #000;
}

.configure-card-bottom {
  justify-content: space-between;
}

.prev-btn,
.next-btn {
  color: rgb(255 255 255);
  font-size: 0.8rem;
  transition: all linear 0.3s;
}

.prev-btn:hover,
.next-btn:hover {
  color: rgb(255 255 255);
  text-decoration: underline;
}

.count,
.count * {
  color: #ffffff;
}

.show-btn {
  color: #fff;
  padding: 2px 6px;
  background-color: rgb(255 255 255 / 10%);
  border-radius: 4px;
  font-size: .7rem;
  font-weight: 500;
  transition: all linear 0.3s;
  border: 1px solid rgb(255 255 255 / 15%);
  cursor: pointer;
  transition: all linear 0.3s;
}

.show-btn .icon {
  display: inline-block;
}

.show-btn.active .icon {
  transform: rotate(180deg);
}

.configure-card-body {
  padding: 16px;
  background: #ffffff;
  display: none;
  border-radius: 12px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  max-height: 465px;
}


.configure-card-body .configure-status {
  border: 2px solid #0064fa;
}

.configure-item-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(0 0 0 / 75%);
}

.configure-item-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 8px;
  border-radius: 6px;
  background: transparent;
  border: 1px solid rgb(226 226 226);
  color: #5d5c5c;
  font-size: 0.8rem;
  font-weight: 500;
  line-height: 1;
  transition: all linear 0.3s;
}

.configure-item-btn:hover {
  background: #0064fa;
  color: #ffffff;
  border-color: #0064fa;
}

.configure-item-btn span{
  color: inherit;
  transition: all linear 0.3s;
}
.configure-item-btn:hover span{
  color: #ffffff;
}
@media (max-width: 575px) {
  .configure-item-btn{
    border: none;
    background-color: transparent !important;
  }
  .configure-item-btn.disabled{
    opacity: 0.5 !important;
  }
}

.configure-item:not(:last-child) {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgb(7 18 81 / 7%);
}

@media (max-width: 991px) {
  .configure-card {
    max-width: 620px;
  }
}

@media (max-width: 767px) {
  .configure-card {
    max-width: 480px;
  }
}

@media (max-width: 575px) {
  .configure-card-title {
    font-size: 0.875rem;
  }

  .count,
  .count * {
    font-size: 0.75rem;
  }

  .configure-item-name {
    font-size: 0.75rem;
  }

  .configure-item-btn {
    padding: 7px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
  }

  .configure-card-header {
    padding: 12px;
  }

  .configure-card-body {
    padding: 12px;
  }

  .configure-card {
    border-radius: 6px;
  }
}


.configure-card-left {
  overflow: hidden;
}

.configure-card-slide {
  display: flex;
  flex-wrap: nowrap;
  transition: transform 0.4s ease;
}

.configure-card-title {
  color: #ffffff;
  flex-shrink: 0;
  width: 100%;
}

.configure-status {
  display: flex;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 2px solid #16def8;
  font-size: 10px;
  color: hsl(var(--white));
  align-items: center;
  justify-content: center;
}

.configure-status i {
  display: none;
}

.configure-status.complete {
  background-color: #16def8;
}

.configure-status.complete i {
  display: block;
}

.configure-card-body .configure-status {
  border: 2px solid #0064fa;
}

.configure-card-body .configure-status.complete {
  background-color: #0064fa;
}

.configure-item-btn.disabled {
  background: #e7e7e7;
  pointer-events: none;
  opacity: 0.75;
}

.configure-card-link.disabled {
  background: rgb(46 153 255);
  pointer-events: none;
  color: #ffffff;
  opacity: 0.85;
}






.configure-card {
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  box-shadow: 1px 20px 20px 13px rgb(0 0 0 / 10%);
  background: #ffffff;
  transform: scale(0);
  transition: all linear 0.15s;
  transform-origin: bottom right;
  display: none;
}

.configure-card.show {
  animation: showCard linear 0.3s forwards;
  display: block;
}
