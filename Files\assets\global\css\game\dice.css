@keyframes spin {
    0% { transform: translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
    33% { transform: translateZ(-100px) rotateX(360deg) rotateY(90deg) rotateZ(180deg); }
    50% { transform: translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(360deg); }
    66% { transform: translateZ(-100px) rotateX(180deg) rotateY(360deg) rotateZ(270deg); }
    83% { transform: translateZ(-100px) rotateX(270deg) rotateY(180deg) rotateZ(180deg); }
    100% { transform: translateZ(-100px) rotateX(280deg) rotateY(0deg) rotateZ(0deg); }
}
.diceRolling {
    animation: spin 50s linear infinite;
}
#dice {
    position: absolute;
    width: 200px;
    height: 200px;
    transform-style: preserve-3d;
}
#wrapper {
    position: relative;
    width: 200px;
    /*padding-top: 50px;*/
    margin: 0 auto;
    perspective: 1200px;
}

#dice span {
    position:absolute;
    margin:100px 0 0 100px;
    display: block;
    font-size: 2.5em;
    padding: 10px;
}
.side {
    position: absolute;
    width: 200px;
    height: 200px;
    background: red;
    box-shadow:inset 0 0 40px red;
    border-radius: 40px;
}
.dot {
    position:absolute;
    width:46px;
    height:46px;
    border-radius:23px;
    background:#444;
    box-shadow:inset 5px 0 10px #000;
}
.dot.center {
    margin:77px 0 0 77px;
}
.dot.dtop {
    margin-top:20px;
}
.dot.dleft {
    margin-left:134px;
}
.dot.dright {
    margin-left:20px;
}
.dot.dbottom {
    margin-top:134px;
}
.dot.center.dleft {
    margin:77px 0 0 20px;
}
.dot.center.dright {
    margin:77px 0 0 134px;
}
#dice .cover, #dice .inner {
    background: red;
    box-shadow: none;
}
#dice .cover {
    border-radius: 0;
    transform: translateZ(0px);
}
#dice .cover.x {
    transform: rotateY(90deg);
}
#dice .cover.z {
    transform: rotateX(90deg);
}
#dice .front  {
    transform: translateZ(100px);
}
#dice .front.inner  {
    transform: translateZ(98px);
}
#dice .back {
    transform: rotateX(-180deg) translateZ(100px);
}
#dice .back.inner {
    transform: rotateX(-180deg) translateZ(98px);
}
#dice .right {
    transform: rotateY(90deg) translateZ(100px);
}
#dice .right.inner {
    transform: rotateY(90deg) translateZ(98px);
}
#dice .left {
    transform: rotateY(-90deg) translateZ(100px);
}
#dice .left.inner {
    transform: rotateY(-90deg) translateZ(98px);
}
#dice .top {
    transform: rotateX(90deg) translateZ(100px);
}
#dice .top.inner {
    transform: rotateX(90deg) translateZ(98px);
}
#dice .bottom {
    transform: rotateX(-90deg) translateZ(100px);
}
#dice .bottom.inner {
    transform: rotateX(-90deg) translateZ(98px);
}

.dices {
    margin-top: 10px;
}

.dices img{
    max-width: 15%;
    cursor: pointer;
    margin-top: 15px;
}

@media (max-width: 1550px){
    .dices {
        margin-top: 19px;
    }
}
@media (max-width: 1255px){
    .dices img {
        max-width: 14%;
    }
}
@media (max-width: 1199px){
    .dices {
        cursor: pointer;
        text-align: center;
        margin-top: 0px;
    }
}

@media (max-width: 1012px){
    .dices img {
        max-width: 14%;
    }
}

@media (max-width: 800px){
    .dices {
        margin-top: 12px;
    }
}

@media (max-width: 620px){
    .dices {
        margin-top: 21px;
    }
}
@media (max-width:575px) {
    .headtail-body {
        position: relative;
        height: 500px;
        border: 4px solid hsl(var(--base));
        border-radius: 12px;
    }
    .headtail-body__shape {
        display: none;
    }
}
