<div class="headtail-body__shape">
    <svg width="100%" height="100%" viewBox="0 0 672 538" fill="none"
         xmlns="http://www.w3.org/2000/svg">
        <g style="mix-blend-mode:screen" opacity="1">
            <path
                  d="M4.43098 89.2602L71.1346 22.5566H195.312L215.137 42.4144H642.78L665.369 65.0043V231.097L643.113 253.387V366.203L665.736 388.827V470.89L644.146 492.514H68.6024L7.99606 431.874V258.118L24.7886 241.326V153.432L3.63135 132.274L4.43098 89.2602Z"
                  fill="hsl(var(--black))" />
        </g>
        <path
              d="M644.712 493.88H68.0359L67.6361 493.48L6.59665 432.474V257.552L23.2559 240.893V154.265L2.09863 133.107L2.93159 88.9604L3.33143 88.5606L70.5681 21.2906H195.879L215.87 41.2816H643.413L666.735 64.6046V231.897L644.479 254.153V365.837L667.102 388.46V471.756L644.712 493.88ZM69.202 491.081H643.413L664.203 470.29V389.426L641.58 366.77V252.787L663.837 230.531V65.5708L642.08 43.8139H214.57L194.746 23.8228H71.7342L5.83031 89.8599L5.064 131.708L26.1879 152.865V241.892L9.52866 258.718V431.308L69.202 491.081Z"
              fill="url(#paint0_linear_89_3334)" />
        <path
              d="M126.11 498.778H66.0037L1.73242 434.473V258.118H14.2601V429.275L71.2014 486.216H126.11V498.778Z"
              fill="url(#paint1_linear_89_3334)" />
        <path
              d="M648.577 496.945L639.714 488.082L659.472 468.291V391.425L638.681 370.635L647.544 361.772L672 386.228V473.489L648.577 496.945Z"
              fill="url(#paint2_linear_89_3334)" />
        <path
              d="M671.634 231.097H659.107V67.6032L640.182 48.6783H608.596V36.1506H645.379L671.634 62.4055V231.097Z"
              fill="url(#paint3_linear_89_3334)" />
        <path
              d="M8.89602 93.6916L0 84.8289L68.5361 16.2928H197.912L217.736 36.1506H265.748V48.6783H212.538L192.714 28.8206H73.7338L8.89602 93.6916Z"
              fill="url(#paint4_linear_89_3334)" />
        <path
              d="M429.808 30.1533H349.077L334.451 15.5264H226.032V14.1271H335.017L349.677 28.7539H429.242L445.601 12.3945H510.072L522.433 0H642.746V1.39942H523L510.639 13.7939H446.167L429.808 30.1533Z"
              fill="url(#paint5_linear_89_3334)" />
        <path d="M532.363 10.2288L522.101 20.4576H536.594L546.823 10.2288H532.363Z"
              fill="url(#paint6_linear_89_3334)" />
        <path d="M561.317 10.2288L551.055 20.4576H565.548L575.777 10.2288H561.317Z"
              fill="url(#paint7_linear_89_3334)" />
        <path d="M619.224 10.2288L608.962 20.4576H623.455L633.684 10.2288H619.224Z"
              fill="url(#paint8_linear_89_3334)" />
        <path d="M590.27 10.2288L580.008 20.4576H594.502L604.731 10.2288H590.27Z"
              fill="url(#paint9_linear_89_3334)" />
        <path d="M670.833 263.882L660.571 253.654V268.114L670.833 278.343V263.882Z"
              fill="url(#paint10_linear_89_3334)" />
        <path d="M670.833 292.836L660.571 282.574V297.067L670.833 307.296V292.836Z"
              fill="url(#paint11_linear_89_3334)" />
        <path d="M670.833 350.744L660.571 340.481V354.975L670.833 365.204V350.744Z"
              fill="url(#paint12_linear_89_3334)" />
        <path d="M670.833 321.79L660.571 311.528V326.021L670.833 336.25V321.79Z"
              fill="url(#paint13_linear_89_3334)" />
        <path d="M2.36523 227.032L12.5607 237.361L12.6607 222.867L2.49851 212.539L2.36523 227.032Z"
              fill="url(#paint14_linear_89_3334)" />
        <path d="M2.79834 169.125L12.9605 179.453L13.0604 164.96L2.89829 154.631L2.79834 169.125Z"
              fill="url(#paint15_linear_89_3334)" />
        <path d="M2.59814 198.078L12.7603 208.407L12.8602 193.914L2.6981 183.585L2.59814 198.078Z"
              fill="url(#paint16_linear_89_3334)" />
        <path d="M509.639 21.7903H226.731V23.1897H509.639V21.7903Z"
              fill="url(#paint17_linear_89_3334)" />
        <path
              d="M432.44 537.127H274.511L274.077 536.694L244.191 506.807H172.389L158.762 493.147L160.728 491.181L173.556 504.009H245.324L245.757 504.408L275.643 534.328H431.274L461.594 504.009H533.362L546.189 491.181L548.189 493.147L534.528 506.807H462.76L432.44 537.127Z"
              fill="url(#paint18_linear_89_3334)" />
        <path d="M305.262 510L295 520.229H309.494L319.722 510H305.262Z"
              fill="url(#paint19_linear_89_3334)" />
        <path d="M334.216 510L323.954 520.229H338.448L348.676 510H334.216Z"
              fill="url(#paint20_linear_89_3334)" />
        <path d="M392.123 510L381.861 520.229H396.355L406.584 510H392.123Z"
              fill="url(#paint21_linear_89_3334)" />
        <path d="M363.17 510L352.908 520.229H367.401L377.63 510H363.17Z"
              fill="url(#paint22_linear_89_3334)" />
        <defs>
            <linearGradient id="paint0_linear_89_3334" x1="334.6" y1="21.2906" x2="334.6"
                            y2="493.88" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint1_linear_89_3334" x1="63.9213" y1="258.118" x2="63.9213"
                            y2="498.778" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint2_linear_89_3334" x1="655.34" y1="361.772" x2="655.34"
                            y2="496.945" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint3_linear_89_3334" x1="640.115" y1="36.1506" x2="640.115"
                            y2="231.097" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint4_linear_89_3334" x1="132.874" y1="16.2928" x2="132.874"
                            y2="93.6916" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint5_linear_89_3334" x1="434.389" y1="0" x2="434.389" y2="30.1533"
                            gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint6_linear_89_3334" x1="534.462" y1="10.2288" x2="534.462"
                            y2="20.4576" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint7_linear_89_3334" x1="563.416" y1="10.2288" x2="563.416"
                            y2="20.4576" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint8_linear_89_3334" x1="621.323" y1="10.2288" x2="621.323"
                            y2="20.4576" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint9_linear_89_3334" x1="592.369" y1="10.2288" x2="592.369"
                            y2="20.4576" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint10_linear_89_3334" x1="665.702" y1="253.654" x2="665.702"
                            y2="278.343" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint11_linear_89_3334" x1="665.702" y1="282.574" x2="665.702"
                            y2="307.296" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint12_linear_89_3334" x1="665.702" y1="340.481" x2="665.702"
                            y2="365.204" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint13_linear_89_3334" x1="665.702" y1="311.528" x2="665.702"
                            y2="336.25" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint14_linear_89_3334" x1="7.51294" y1="212.539" x2="7.51294"
                            y2="237.361" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint15_linear_89_3334" x1="7.92937" y1="154.631" x2="7.92937"
                            y2="179.453" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint16_linear_89_3334" x1="7.72919" y1="183.585" x2="7.72919"
                            y2="208.407" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint17_linear_89_3334" x1="368.185" y1="21.7903" x2="368.185"
                            y2="23.1897" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint18_linear_89_3334" x1="353.475" y1="491.181" x2="353.475"
                            y2="537.127" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint19_linear_89_3334" x1="307.361" y1="510" x2="307.361"
                            y2="520.229" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint20_linear_89_3334" x1="336.315" y1="510" x2="336.315"
                            y2="520.229" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint21_linear_89_3334" x1="394.222" y1="510" x2="394.222"
                            y2="520.229" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
            <linearGradient id="paint22_linear_89_3334" x1="365.269" y1="510" x2="365.269"
                            y2="520.229" gradientUnits="userSpaceOnUse">
                <stop offset="100%" stop-color="hsl(var(--base-two))" />
                <stop offset="1" stop-color="hsl(var(--base))" />
            </linearGradient>
        </defs>
    </svg>
</div>