.doc-nav {
    height: calc(100vh - 66px);
    background: hsl(var(--light)/.05);

    @include lg-screen {
        height: calc(100vh - 104px);
        background: transparent;
    }

    &__list {
        gap: 1px;

        &:first-child {
            padding-top: 20px;
        }

        &:last-child {
            padding-bottom: 20px;
        }
    }

    &__link {
        display: block;
        padding: 10px 15px;
        position: relative;
        text-transform: uppercase;
        font-family: var(--heading-font);
        font-size: 14px;
        font-weight: 500;
        color: hsl(var(--light));
        text-decoration: none;
        transition: unset;
        &:hover {
            color: hsl(var(--light));
            background: var(--canvas);
            text-decoration: none;
        }

        &.active {
            color: hsl(var(--light));
            background: var(--canvas);
        }
    }
}